@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;

    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;

    --primary: 142.1 76.2% 36.3%;
    --primary-foreground: 355.7 100% 97.3%;

    --secondary: 220 14.3% 95.9%;
    --secondary-foreground: 220.9 39.3% 11%;

    --muted: 220 14.3% 95.9%;
    --muted-foreground: 220 8.9% 46.1%;

    --accent: 220 14.3% 95.9%;
    --accent-foreground: 220.9 39.3% 11%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 20% 98%;

    --border: 220 13% 91%;
    --input: 220 13% 91%;
    --ring: 142.1 76.2% 36.3%;

    --radius: 0.5rem;

    /* Chart colors */
    --chart-1: 142.1 76.2% 36.3%;
    --chart-2: 221.2 83.2% 53.3%;
    --chart-3: 262.1 83.3% 57.8%;
    --chart-4: 24.6 95% 53.1%;
  }

  [data-color-scheme="blue"] {
    --primary: 221.2 83.2% 53.3%;
    --primary-foreground: 210 40% 98%;
    --ring: 221.2 83.2% 53.3%;
    --chart-1: 221.2 83.2% 53.3%;
  }

  [data-color-scheme="purple"] {
    --primary: 262.1 83.3% 57.8%;
    --primary-foreground: 210 40% 98%;
    --ring: 262.1 83.3% 57.8%;
    --chart-1: 262.1 83.3% 57.8%;
  }

  [data-color-scheme="orange"] {
    --primary: 24.6 95% 53.1%;
    --primary-foreground: 60 9.1% 97.8%;
    --ring: 24.6 95% 53.1%;
    --chart-1: 24.6 95% 53.1%;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;

    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;

    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    --primary: 142.1 70.6% 45.3%;
    --primary-foreground: 144.9 80.4% 10%;

    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 142.1 76.2% 36.3%;
  }

  .dark[data-color-scheme="blue"] {
    --primary: 217.2 91.2% 59.8%;
    --primary-foreground: 222.2 47.4% 11.2%;
    --ring: 217.2 91.2% 59.8%;
    --chart-1: 217.2 91.2% 59.8%;
  }

  .dark[data-color-scheme="purple"] {
    --primary: 263.4 70% 50.4%;
    --primary-foreground: 210 40% 98%;
    --ring: 263.4 70% 50.4%;
    --chart-1: 263.4 70% 50.4%;
  }

  .dark[data-color-scheme="orange"] {
    --primary: 20.5 90.2% 48.2%;
    --primary-foreground: 60 9.1% 97.8%;
    --ring: 20.5 90.2% 48.2%;
    --chart-1: 20.5 90.2% 48.2%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

/* Enhanced Sonner Toast Styles */
@layer components {
  /* Make close button more visible and accessible */
  .toaster [data-sonner-toast] [data-close-button] {
    @apply opacity-100 !important;
    @apply bg-background border border-border rounded-md !important;
    @apply hover:bg-muted transition-colors duration-200 !important;
    @apply focus:ring-2 focus:ring-ring focus:ring-offset-2 !important;
    @apply w-6 h-6 flex items-center justify-center !important;
    @apply text-muted-foreground hover:text-foreground !important;
    @apply absolute top-2 right-2 !important;
  }

  /* Enhanced toast container */
  .toaster [data-sonner-toast] {
    @apply border border-border !important;
    @apply shadow-lg !important;
    @apply backdrop-blur-sm !important;
    @apply pr-10 !important; /* Add padding for close button */
  }

  /* Success toast styling */
  .toaster [data-sonner-toast][data-type="success"] {
    @apply border-green-200 bg-green-50 text-green-900 !important;
  }

  .dark .toaster [data-sonner-toast][data-type="success"] {
    @apply border-green-800 bg-green-950 text-green-100 !important;
  }

  /* Error toast styling */
  .toaster [data-sonner-toast][data-type="error"] {
    @apply border-red-200 bg-red-50 text-red-900 !important;
  }

  .dark .toaster [data-sonner-toast][data-type="error"] {
    @apply border-red-800 bg-red-950 text-red-100 !important;
  }

  /* Warning toast styling */
  .toaster [data-sonner-toast][data-type="warning"] {
    @apply border-yellow-200 bg-yellow-50 text-yellow-900 !important;
  }

  .dark .toaster [data-sonner-toast][data-type="warning"] {
    @apply border-yellow-800 bg-yellow-950 text-yellow-100 !important;
  }

  /* Info toast styling */
  .toaster [data-sonner-toast][data-type="info"] {
    @apply border-blue-200 bg-blue-50 text-blue-900 !important;
  }

  .dark .toaster [data-sonner-toast][data-type="info"] {
    @apply border-blue-800 bg-blue-950 text-blue-100 !important;
  }

  /* Close button hover effect */
  .toaster [data-sonner-toast] [data-close-button]:hover {
    @apply scale-110 !important;
  }

  /* Toast animation improvements */
  .toaster [data-sonner-toast] {
    @apply transition-all duration-300 ease-in-out !important;
    animation: slideInFromTop 0.3s ease-out;
  }

  .toaster [data-sonner-toast][data-removed="true"] {
    @apply transition-all duration-300 ease-in-out !important;
    animation: slideOutToRight 0.3s ease-in;
  }

  /* Custom keyframe animations */
  @keyframes slideInFromTop {
    from {
      opacity: 0;
      transform: translateY(-10px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  @keyframes slideOutToRight {
    from {
      opacity: 1;
      transform: translateX(0);
    }
    to {
      opacity: 0;
      transform: translateX(100%);
    }
  }

  /* Landing page blob animations */
  @keyframes blob {
    0% {
      transform: translate(0px, 0px) scale(1);
    }
    33% {
      transform: translate(30px, -50px) scale(1.1);
    }
    66% {
      transform: translate(-20px, 20px) scale(0.9);
    }
    100% {
      transform: translate(0px, 0px) scale(1);
    }
  }

  .animate-blob {
    animation: blob 7s infinite;
  }

  .animation-delay-2000 {
    animation-delay: 2s;
  }

  .animation-delay-4000 {
    animation-delay: 4s;
  }

  /* Premium landing page animations */
  @keyframes float {
    0%, 100% {
      transform: translateY(0px);
    }
    50% {
      transform: translateY(-20px);
    }
  }

  @keyframes glow {
    0%, 100% {
      box-shadow: 0 0 20px rgba(16, 185, 129, 0.3);
    }
    50% {
      box-shadow: 0 0 40px rgba(16, 185, 129, 0.6);
    }
  }

  @keyframes shimmer {
    0% {
      background-position: -200% 0;
    }
    100% {
      background-position: 200% 0;
    }
  }

  @keyframes pulse-glow {
    0%, 100% {
      opacity: 0.3;
    }
    50% {
      opacity: 0.8;
    }
  }

  .animate-float {
    animation: float 6s ease-in-out infinite;
  }

  .animate-glow {
    animation: glow 2s ease-in-out infinite alternate;
  }

  .animate-shimmer {
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    background-size: 200% 100%;
    animation: shimmer 2s infinite;
  }

  .animate-pulse-glow {
    animation: pulse-glow 2s ease-in-out infinite;
  }

  /* Gradient text animation */
  @keyframes gradient-shift {
    0%, 100% {
      background-position: 0% 50%;
    }
    50% {
      background-position: 100% 50%;
    }
  }

  .animate-gradient {
    background-size: 200% 200%;
    animation: gradient-shift 3s ease infinite;
  }

  /* Hover effects */
  .hover-lift {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .hover-lift:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  }

  /* Custom scrollbar */
  ::-webkit-scrollbar {
    width: 6px;
  }

  ::-webkit-scrollbar-track {
    background: hsl(var(--muted));
    border-radius: 3px;
  }

  ::-webkit-scrollbar-thumb {
    background: hsl(var(--primary));
    border-radius: 3px;
  }

  ::-webkit-scrollbar-thumb:hover {
    background: hsl(var(--primary) / 0.8);
  }

  /* Thin scrollbar for order form */
  .order-form-scroll::-webkit-scrollbar {
    width: 4px;
  }

  .order-form-scroll::-webkit-scrollbar-track {
    background: transparent;
  }

  .order-form-scroll::-webkit-scrollbar-thumb {
    background: hsl(var(--border));
    border-radius: 2px;
  }

  .order-form-scroll::-webkit-scrollbar-thumb:hover {
    background: hsl(var(--primary) / 0.6);
  }

  /* Custom slider styling for trading order form */
  .slider::-webkit-slider-thumb {
    appearance: none;
    height: 20px;
    width: 20px;
    border-radius: 50%;
    background: hsl(var(--primary));
    cursor: pointer;
    border: 2px solid white;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2);
    transition: all 0.2s ease;
  }

  .slider::-webkit-slider-thumb:hover {
    transform: scale(1.1);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
  }

  .slider::-moz-range-thumb {
    height: 20px;
    width: 20px;
    border-radius: 50%;
    background: hsl(var(--primary));
    cursor: pointer;
    border: 2px solid white;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2);
    transition: all 0.2s ease;
  }

  .slider::-moz-range-thumb:hover {
    transform: scale(1.1);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
  }
}

/* Custom responsive utilities */
@layer utilities {
  /* Mobile-first responsive text sizes */
  .text-responsive-xs {
    @apply text-xs sm:text-sm;
  }

  .text-responsive-sm {
    @apply text-sm sm:text-base;
  }

  .text-responsive-base {
    @apply text-base sm:text-lg;
  }

  .text-responsive-lg {
    @apply text-lg sm:text-xl;
  }

  .text-responsive-xl {
    @apply text-xl sm:text-2xl;
  }

  /* Mobile-first responsive spacing */
  .p-responsive {
    @apply p-2 sm:p-3 md:p-4 lg:p-6;
  }

  .px-responsive {
    @apply px-2 sm:px-3 md:px-4 lg:px-6;
  }

  .py-responsive {
    @apply py-2 sm:py-3 md:py-4 lg:py-6;
  }

  .m-responsive {
    @apply m-2 sm:m-3 md:m-4 lg:m-6;
  }

  .gap-responsive {
    @apply gap-2 sm:gap-3 md:gap-4 lg:gap-6;
  }

  /* Mobile-optimized containers */
  .container-mobile {
    @apply w-full max-w-full px-2 sm:px-4 md:px-6 lg:px-8;
  }

  /* Touch-friendly interactive elements */
  .touch-target {
    @apply min-h-[44px] min-w-[44px] flex items-center justify-center;
  }

  /* Mobile-optimized cards */
  .card-mobile {
    @apply rounded-lg border bg-card text-card-foreground shadow-sm p-3 sm:p-4 md:p-6;
  }

  /* Responsive grid layouts */
  .grid-responsive-1 {
    @apply grid grid-cols-1;
  }

  .grid-responsive-2 {
    @apply grid grid-cols-1 sm:grid-cols-2;
  }

  .grid-responsive-3 {
    @apply grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3;
  }

  .grid-responsive-4 {
    @apply grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4;
  }

  /* Mobile navigation */
  .nav-mobile {
    @apply fixed inset-x-0 bottom-0 z-50 bg-background border-t border-border p-2 sm:hidden;
  }

  /* Responsive table */
  .table-responsive {
    @apply w-full overflow-x-auto;
  }

  .table-responsive table {
    @apply min-w-full;
  }

  /* Mobile-first flex utilities */
  .flex-mobile-col {
    @apply flex flex-col sm:flex-row;
  }

  .flex-mobile-row {
    @apply flex flex-row;
  }

  /* Responsive heights for trading interface */
  .trading-height {
    @apply h-[300px] sm:h-[400px] md:h-[500px] lg:h-[600px];
  }

  .sidebar-height {
    @apply h-[calc(100vh-80px)] sm:h-[calc(100vh-100px)] lg:h-[calc(100vh-120px)];
  }

  /* Mobile drawer overlay */
  .drawer-overlay {
    @apply fixed inset-0 z-40 bg-black/50 backdrop-blur-sm;
  }

  /* Responsive button sizes */
  .btn-responsive {
    @apply px-3 py-2 text-sm sm:px-4 sm:py-2 sm:text-base;
  }

  .btn-responsive-sm {
    @apply px-2 py-1 text-xs sm:px-3 sm:py-1.5 sm:text-sm;
  }

  /* Hide scrollbar but keep functionality */
  .scrollbar-hide {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }

  .scrollbar-hide::-webkit-scrollbar {
    display: none;
  }

  /* Mobile-optimized form inputs */
  .input-mobile {
    @apply w-full px-3 py-2 text-base sm:text-sm border border-input bg-background rounded-md;
  }

  /* Responsive modal */
  .modal-responsive {
    @apply w-full max-w-lg mx-4 sm:mx-auto;
  }

  /* Trading panel responsive */
  .trading-panel {
    @apply w-full max-w-[350px] lg:max-w-none mx-auto;
  }

  /* Mobile sidebar - Simplified */
  .mobile-sidebar {
    @apply fixed inset-y-0 left-0 z-30 w-80 bg-background border-r border-border transform transition-transform duration-300 ease-in-out shadow-xl;
  }

  .mobile-sidebar.closed {
    @apply -translate-x-full;
  }

  .mobile-sidebar.open {
    @apply translate-x-0;
  }

  /* Responsive trading layout */
  .trading-layout-mobile {
    @apply flex flex-col lg:grid lg:grid-cols-4 xl:grid-cols-5;
  }

  .trading-main-mobile {
    @apply order-1 lg:col-span-3 xl:col-span-4;
  }

  .trading-sidebar-mobile {
    @apply order-2 lg:order-1 lg:col-span-1 xl:col-span-1;
  }

  /* Mobile-optimized auth status */
  .auth-status-mobile {
    @apply flex flex-col sm:flex-row items-start sm:items-center gap-2 p-2 rounded-lg border;
  }

  /* Responsive price ticker */
  .price-ticker-mobile {
    @apply flex flex-col sm:flex-row items-start sm:items-center gap-2 sm:gap-4 p-3 bg-card rounded-lg border;
  }

  /* Mobile-optimized tables - Enhanced for better mobile experience */
  .trading-table-mobile {
    @apply w-full overflow-x-auto;
  }

  .trading-table-mobile table {
    @apply min-w-[800px] sm:min-w-full;
  }

  .trading-table-mobile th,
  .trading-table-mobile td {
    @apply px-2 py-2 sm:px-3 sm:py-3 text-xs sm:text-sm whitespace-nowrap;
  }

  /* Position panel mobile optimization */
  .positions-panel-mobile {
    @apply bg-card rounded-lg border border-border p-2 sm:p-4;
  }

  .positions-panel-mobile .table-container {
    @apply overflow-x-auto -mx-2 sm:-mx-4 px-2 sm:px-4;
  }

  .positions-panel-mobile table {
    @apply min-w-[900px] sm:min-w-full text-xs sm:text-sm;
  }

  .positions-panel-mobile th {
    @apply pb-2 font-medium text-muted-foreground text-xs uppercase tracking-wide;
  }

  .positions-panel-mobile td {
    @apply py-2 sm:py-3 border-b border-border/50 last:border-0;
  }

  /* Mobile PnL display optimization */
  .pnl-mobile {
    @apply flex flex-col sm:flex-row sm:items-center gap-1 sm:gap-2;
  }

  .pnl-value {
    @apply font-medium;
  }

  .pnl-percentage {
    @apply text-xs opacity-80;
  }

  /* Mobile chart container */
  .chart-container-mobile {
    @apply w-full h-[300px] sm:h-[400px] md:h-[500px] lg:h-[600px] rounded-lg overflow-hidden;
  }

  /* Mobile order form */
  .order-form-mobile {
    @apply w-full max-w-none sm:max-w-[350px] mx-auto lg:max-w-none;
  }

  /* Responsive tabs */
  .tabs-mobile {
    @apply flex overflow-x-auto scrollbar-hide border-b border-border;
  }

  .tab-mobile {
    @apply flex-shrink-0 px-3 py-2 text-sm font-medium whitespace-nowrap border-b-2 border-transparent;
  }

  .tab-mobile.active {
    @apply border-primary text-primary;
  }

  .tab-mobile.inactive {
    @apply text-muted-foreground hover:text-foreground;
  }
}