"use client"

import type React from "react"

import { useEffect, useState, Suspense, useRef } from "react"
import { useRouter } from "next/navigation"
import SideNavigation from "@/components/side-navigation"
import AccountDropdown from "@/components/account-dropdown"
import MobileSidebarToggle from "@/components/mobile-sidebar-toggle"
import NotificationDropdown from "@/components/notifications/notification-dropdown"
import { TradingProvider } from "@/contexts/trading-context"
import { useAuth } from "@/contexts/auth-context"

export default function DashboardLayout({ children }: { children: React.ReactNode }) {
  const router = useRouter()
  const [isLoading, setIsLoading] = useState(true)
  const [mobileSidebarOpen, setMobileSidebarOpen] = useState(false)
  const [notificationDropdownOpen, setNotificationDropdownOpen] = useState(false)
  const [sidebarVisible, setSidebarVisible] = useState(true)
  const [isClient, setIsClient] = useState(false)
  const { user, loading } = useAuth()
  const notificationDropdownRef = useRef<HTMLDivElement>(null)

  // Check if user is logged in using Firebase Auth
  useEffect(() => {
    setIsClient(true)

    if (!loading) {
      if (!user) {
        router.push("/login")
      } else {
        setIsLoading(false)
      }
    }
  }, [user, loading, router])

  // Close sidebar when clicking outside on mobile
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const sidebar = document.getElementById("mobile-sidebar")
      const toggle = document.getElementById("mobile-sidebar-toggle")

      if (
        mobileSidebarOpen &&
        sidebar &&
        toggle &&
        !sidebar.contains(event.target as Node) &&
        !toggle.contains(event.target as Node)
      ) {
        setMobileSidebarOpen(false)
      }
    }

    document.addEventListener("mousedown", handleClickOutside)
    return () => {
      document.removeEventListener("mousedown", handleClickOutside)
    }
  }, [mobileSidebarOpen])

  // Prevent body scroll when mobile sidebar is open
  useEffect(() => {
    if (mobileSidebarOpen) {
      document.body.style.overflow = "hidden"
    } else {
      document.body.style.overflow = "auto"
    }

    return () => {
      document.body.style.overflow = "auto"
    }
  }, [mobileSidebarOpen])

  // Close notification dropdown when clicking outside
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (notificationDropdownRef.current && !notificationDropdownRef.current.contains(event.target as Node)) {
        setNotificationDropdownOpen(false)
      }
    }

    document.addEventListener("mousedown", handleClickOutside)
    return () => {
      document.removeEventListener("mousedown", handleClickOutside)
    }
  }, [])

  // Handle window resize to update sidebar behavior
  useEffect(() => {
    const handleResize = () => {
      // Force re-render to update sidebar width calculation
      setIsClient(true)
    }

    if (isClient) {
      window.addEventListener("resize", handleResize)
      return () => window.removeEventListener("resize", handleResize)
    }
  }, [isClient])

  if (isLoading || loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-background">
        <div className="text-center">
          <svg
            className="animate-spin h-12 w-12 text-primary mx-auto"
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 24 24"
          >
            <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
            <path
              className="opacity-75"
              fill="currentColor"
              d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
            ></path>
          </svg>
          <p className="mt-4 text-lg font-medium text-foreground">Loading...</p>
        </div>
      </div>
    )
  }

  return (
    <TradingProvider>
      <div className="min-h-screen w-full bg-background text-foreground">
        <div className="h-full w-full">
          <div className="bg-card shadow-lg h-full">
          {/* Header */}
          <header className="relative flex items-center justify-between px-4 py-2 border-b border-border bg-card/50 backdrop-blur-sm z-50">
            <div className="flex items-center space-x-4">
              <div className="lg:hidden">
                <MobileSidebarToggle onToggle={setMobileSidebarOpen} isOpen={mobileSidebarOpen} />
              </div>
              <div className="flex items-center">
                <h1 className="text-primary font-bold text-xl tracking-tight bg-gradient-to-r from-primary via-primary/90 to-primary/70 bg-clip-text text-transparent">
                  ThePaperBull
                </h1>
                <span className="ml-2 text-xs bg-gradient-to-r from-primary/20 to-primary/30 text-primary px-2 py-0.5 rounded-full font-semibold border border-primary/20">BETA</span>
              </div>
            </div>

            <div className="flex items-center space-x-3 sm:space-x-5 relative z-50" ref={notificationDropdownRef}>
              <NotificationDropdown
                isOpen={notificationDropdownOpen}
                onToggle={() => setNotificationDropdownOpen(!notificationDropdownOpen)}
                onClose={() => setNotificationDropdownOpen(false)}
              />

              <AccountDropdown />
            </div>
          </header>

          <div className="relative h-[calc(100vh-60px)]">
            {/* Mobile Sidebar Overlay */}
            {mobileSidebarOpen && (
              <div
                className="fixed inset-0 bg-black/50 backdrop-blur-sm z-20 md:hidden"
                onClick={() => setMobileSidebarOpen(false)}
              />
            )}

            {/* Sidebar - Fixed positioning */}
            <div
              id="sidebar"
              className={`${
                mobileSidebarOpen ? "translate-x-0" : "-translate-x-full"
              } fixed inset-y-0 left-0 z-30 w-80 transform transition-all duration-300 ease-in-out md:translate-x-0 md:relative md:z-auto shadow-xl`}
              style={{
                top: "60px", // Account for header height
                height: "calc(100vh - 60px)",
                transform: isClient && window.innerWidth >= 768
                  ? `translateX(${sidebarVisible ? "0" : "-100%"})`
                  : mobileSidebarOpen ? "translateX(0)" : "translateX(-100%)"
              }}
            >
              <SideNavigation onCloseMobile={() => setMobileSidebarOpen(false)} />
            </div>

            {/* Sidebar Toggle Button (Desktop) - Fixed position */}
            <div className="hidden md:flex fixed left-0 top-1/2 transform -translate-y-1/2 z-40">
              <button
                onClick={() => setSidebarVisible(!sidebarVisible)}
                className="bg-gradient-to-r from-primary to-primary/80 text-primary-foreground rounded-r-xl p-3 shadow-lg hover:shadow-xl transition-all duration-300 group"
                style={{
                  transform: `translateX(${sidebarVisible ? "320px" : "0px"})` // 320px = w-80
                }}
                aria-label={sidebarVisible ? "Hide sidebar" : "Show sidebar"}
              >
                {sidebarVisible ? (
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="18"
                    height="18"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    className="group-hover:scale-110 transition-transform duration-200"
                  >
                    <path d="m15 18-6-6 6-6" />
                  </svg>
                ) : (
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="18"
                    height="18"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    className="group-hover:scale-110 transition-transform duration-200"
                  >
                    <path d="m9 18 6-6-6-6" />
                  </svg>
                )}
              </button>
            </div>

            {/* Main Content Area - Fixed positioning */}
            <main
              className="flex-1 overflow-auto transition-all duration-300 ease-in-out"
              style={{
                marginLeft: isClient && window.innerWidth >= 768 ? (sidebarVisible ? "320px" : "0px") : "0px"
              }}
            >
              <div className="h-full w-full">
                <Suspense fallback={
                  <div className="flex items-center justify-center h-full">
                    <div className="text-center">
                      <svg
                        className="animate-spin h-8 w-8 text-primary mx-auto"
                        xmlns="http://www.w3.org/2000/svg"
                        fill="none"
                        viewBox="0 0 24 24"
                      >
                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                        <path
                          className="opacity-75"
                          fill="currentColor"
                          d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                        ></path>
                      </svg>
                      <p className="mt-2 text-sm text-muted-foreground">Loading...</p>
                    </div>
                  </div>
                }>
                  {children}
                </Suspense>
              </div>
            </main>
          </div>
          </div>
        </div>
      </div>
    </TradingProvider>
  )
}
