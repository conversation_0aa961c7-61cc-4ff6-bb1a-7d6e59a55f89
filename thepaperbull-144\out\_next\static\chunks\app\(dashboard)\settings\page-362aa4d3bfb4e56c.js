(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[670],{381:(e,r,t)=>{"use strict";t.d(r,{A:()=>a});let a=(0,t(19946).A)("Settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},5196:(e,r,t)=>{"use strict";t.d(r,{A:()=>a});let a=(0,t(19946).A)("Check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]])},23861:(e,r,t)=>{"use strict";t.d(r,{A:()=>a});let a=(0,t(19946).A)("Bell",[["path",{d:"M6 8a6 6 0 0 1 12 0c0 7 3 9 3 9H3s3-2 3-9",key:"1qo2s2"}],["path",{d:"M10.3 21a1.94 1.94 0 0 0 3.4 0",key:"qgo35s"}]])},25552:(e,r,t)=>{"use strict";t.d(r,{G6:()=>l,Ru:()=>o,bk:()=>n,signOutUser:()=>d});var a=t(16203),s=t(98915);let i=new a.HF;i.setCustomParameters({prompt:"select_account"});let n=async(e,r,t)=>{try{let i=await (0,a.eJ)(s.j2,e,r);return t&&i.user&&await (0,a.r7)(i.user,{displayName:t}),i.user}catch(e){throw console.error("Sign up error:",e),Error(c(e.code))}},o=async(e,r)=>{try{return(await (0,a.x9)(s.j2,e,r)).user}catch(e){throw console.error("Sign in error:",e),Error(c(e.code))}},l=async()=>{try{return(await (0,a.df)(s.j2,i)).user}catch(e){if(console.error("Google sign in error:",e),"auth/popup-closed-by-user"===e.code||"auth/cancelled-popup-request"===e.code)throw e;throw Error(c(e.code))}},d=async()=>{try{await (0,a.CI)(s.j2)}catch(e){throw console.error("Sign out error:",e),Error("Failed to sign out")}},c=e=>{switch(e){case"auth/user-not-found":return"No account found with this email address. Please check your email or sign up for a new account.";case"auth/wrong-password":return"Incorrect password. Please try again or reset your password.";case"auth/invalid-credential":return"Invalid email or password. Please check your credentials and try again.";case"auth/email-already-in-use":return"An account with this email already exists. Please sign in instead.";case"auth/weak-password":return"Password should be at least 6 characters long.";case"auth/invalid-email":return"Please enter a valid email address.";case"auth/user-disabled":return"This account has been disabled. Please contact support.";case"auth/too-many-requests":return"Too many failed attempts. Please try again later or reset your password.";case"auth/popup-closed-by-user":return"Sign-in popup was closed. Please try again.";case"auth/popup-blocked":return"Sign-in popup was blocked. Please allow popups and try again.";case"auth/network-request-failed":return"Network error. Please check your internet connection and try again.";case"auth/cancelled-popup-request":return"Sign-in was cancelled.";case"auth/account-exists-with-different-credential":return"An account already exists with the same email address but different sign-in credentials.";case"auth/operation-not-allowed":return"This sign-in method is not enabled. Please contact support.";case"auth/invalid-api-key":return"Invalid API key. Please check your Firebase configuration.";case"auth/app-deleted":return"Firebase app has been deleted. Please check your configuration.";case"auth/invalid-user-token":return"User token is invalid. Please sign in again.";case"auth/user-token-expired":return"User token has expired. Please sign in again.";default:return"Authentication error: ".concat(e||"Unknown error")}}},34869:(e,r,t)=>{"use strict";t.d(r,{A:()=>a});let a=(0,t(19946).A)("Globe",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 2a14.5 14.5 0 0 0 0 20 14.5 14.5 0 0 0 0-20",key:"13o1zl"}],["path",{d:"M2 12h20",key:"9i4pu4"}]])},35695:(e,r,t)=>{"use strict";var a=t(18999);t.o(a,"usePathname")&&t.d(r,{usePathname:function(){return a.usePathname}}),t.o(a,"useRouter")&&t.d(r,{useRouter:function(){return a.useRouter}}),t.o(a,"useSearchParams")&&t.d(r,{useSearchParams:function(){return a.useSearchParams}})},36443:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>M});var a=t(95155),s=t(12115),i=t(35695),n=t(19946);let o=(0,n.A)("LoaderCircle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]]);var l=t(381),d=t(71007),c=t(23861),m=t(93509),u=t(34869),g=t(81586),h=t(75525);let x=(0,n.A)("Save",[["path",{d:"M15.2 3a2 2 0 0 1 1.4.6l3.8 3.8a2 2 0 0 1 .6 1.4V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2z",key:"1c8476"}],["path",{d:"M17 21v-7a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v7",key:"1ydtos"}],["path",{d:"M7 3v4a1 1 0 0 0 1 1h7",key:"t51u73"}]]);var p=t(62098),b=t(97636),y=t(50475),f=t(91317),v=t(16203),j=t(98915),k=t(25552);let N=(0,n.A)("PenLine",[["path",{d:"M12 20h9",key:"t2du7b"}],["path",{d:"M16.376 3.622a1 1 0 0 1 3.002 3.002L7.368 18.635a2 2 0 0 1-.855.506l-2.872.838a.5.5 0 0 1-.62-.62l.838-2.872a2 2 0 0 1 .506-.854z",key:"1ykcvy"}]]);var w=t(54416),S=t(5196);function C(e){let{content:r,children:t,position:i="top",className:n="",delay:o=300}=e,[l,d]=(0,s.useState)(!1),[c,m]=(0,s.useState)(i),u=(0,s.useRef)(),g=(0,s.useRef)(null),h=(0,s.useRef)(null),x=()=>{u.current&&clearTimeout(u.current),u.current=setTimeout(()=>{d(!0),b()},o)},p=()=>{u.current&&clearTimeout(u.current),d(!1)},b=()=>{if(!g.current||!h.current)return;let e=g.current,r=h.current.getBoundingClientRect(),t=e.getBoundingClientRect(),a={width:window.innerWidth,height:window.innerHeight},s=i;switch(i){case"top":r.top-t.height<0&&(s="bottom");break;case"bottom":r.bottom+t.height>a.height&&(s="top");break;case"left":r.left-t.width<0&&(s="right");break;case"right":r.right+t.width>a.width&&(s="left")}m(s)};return(0,s.useEffect)(()=>()=>{u.current&&clearTimeout(u.current)},[]),(0,a.jsxs)("div",{ref:h,className:"relative inline-block ".concat(n),onMouseEnter:x,onMouseLeave:p,onFocus:x,onBlur:p,children:[t,l&&(0,a.jsxs)("div",{ref:g,className:(()=>{let e="absolute z-50 px-3 py-2 text-sm bg-gray-900 text-white rounded-lg shadow-lg pointer-events-none transition-all duration-200";switch(c){case"top":return"".concat(e," bottom-full left-1/2 transform -translate-x-1/2 mb-2");case"bottom":return"".concat(e," top-full left-1/2 transform -translate-x-1/2 mt-2");case"left":return"".concat(e," right-full top-1/2 transform -translate-y-1/2 mr-2");case"right":return"".concat(e," left-full top-1/2 transform -translate-y-1/2 ml-2");default:return e}})(),style:{opacity:+!!l,transform:l?"scale(1)":"scale(0.95)"},children:[r,(0,a.jsx)("div",{className:(()=>{let e="absolute w-2 h-2 bg-gray-900 transform rotate-45";switch(c){case"top":return"".concat(e," top-full left-1/2 transform -translate-x-1/2 -translate-y-1/2");case"bottom":return"".concat(e," bottom-full left-1/2 transform -translate-x-1/2 translate-y-1/2");case"left":return"".concat(e," left-full top-1/2 transform -translate-x-1/2 -translate-y-1/2");case"right":return"".concat(e," right-full top-1/2 transform translate-x-1/2 -translate-y-1/2");default:return e}})()})]})]})}let A=[{id:"bull-trader",name:"Bull Trader",emoji:"\uD83D\uDC02",bgColor:"bg-gradient-to-br from-green-500 to-green-600",description:"Optimistic market outlook",category:"Market Sentiment"},{id:"bear-trader",name:"Bear Trader",emoji:"\uD83D\uDC3B",bgColor:"bg-gradient-to-br from-red-500 to-red-600",description:"Cautious and defensive",category:"Market Sentiment"},{id:"diamond-hands",name:"Diamond Hands",emoji:"\uD83D\uDC8E",bgColor:"bg-gradient-to-br from-blue-500 to-purple-600",description:"Long-term HODL strategy",category:"Investment Style"},{id:"rocket-trader",name:"Rocket Trader",emoji:"\uD83D\uDE80",bgColor:"bg-gradient-to-br from-blue-600 to-indigo-600",description:"Aiming for the moon",category:"Investment Style"},{id:"chart-master",name:"Chart Master",emoji:"\uD83D\uDCC8",bgColor:"bg-gradient-to-br from-emerald-500 to-teal-600",description:"Technical analysis expert",category:"Trading Style"},{id:"lightning-fast",name:"Lightning Fast",emoji:"⚡",bgColor:"bg-gradient-to-br from-yellow-400 to-orange-500",description:"High-frequency trading",category:"Trading Style"},{id:"whale-trader",name:"Whale Trader",emoji:"\uD83D\uDC0B",bgColor:"bg-gradient-to-br from-blue-700 to-blue-800",description:"Big capital movements",category:"Capital Size"},{id:"crypto-king",name:"Crypto King",emoji:"\uD83D\uDC51",bgColor:"bg-gradient-to-br from-yellow-500 to-amber-600",description:"Cryptocurrency royalty",category:"Expertise"},{id:"ninja-trader",name:"Ninja Trader",emoji:"\uD83E\uDD77",bgColor:"bg-gradient-to-br from-gray-700 to-gray-800",description:"Stealth market entry",category:"Trading Style"},{id:"robot-trader",name:"Robot Trader",emoji:"\uD83E\uDD16",bgColor:"bg-gradient-to-br from-slate-500 to-slate-600",description:"Algorithmic strategies",category:"Trading Style"},{id:"fire-trader",name:"Fire Trader",emoji:"\uD83D\uDD25",bgColor:"bg-gradient-to-br from-orange-500 to-red-500",description:"Hot streak performer",category:"Performance"},{id:"target-trader",name:"Target Trader",emoji:"\uD83C\uDFAF",bgColor:"bg-gradient-to-br from-red-600 to-pink-600",description:"Precision execution",category:"Trading Style"},{id:"brain-trader",name:"Brain Trader",emoji:"\uD83E\uDDE0",bgColor:"bg-gradient-to-br from-purple-500 to-pink-500",description:"Strategic intelligence",category:"Expertise"},{id:"crystal-ball",name:"Fortune Teller",emoji:"\uD83D\uDD2E",bgColor:"bg-gradient-to-br from-purple-600 to-indigo-600",description:"Market prediction",category:"Expertise"},{id:"money-maker",name:"Money Maker",emoji:"\uD83D\uDCB0",bgColor:"bg-gradient-to-br from-green-600 to-emerald-600",description:"Profit maximization",category:"Performance"},{id:"star-trader",name:"Star Trader",emoji:"⭐",bgColor:"bg-gradient-to-br from-amber-500 to-yellow-500",description:"Rising market star",category:"Performance"}];function P(e){let{selectedAvatar:r,onSelect:t,className:i=""}=e,[n,o]=(0,s.useState)(!1),l=A.find(e=>e.id===r)||A[0],d=e=>{t(e),o(!1)},c=A.reduce((e,r)=>(e[r.category]||(e[r.category]=[]),e[r.category].push(r),e),{});return(0,a.jsxs)("div",{className:"".concat(i),children:[(0,a.jsxs)("div",{className:"flex items-center space-x-6 p-6 border-2 border-gray-200 dark:border-gray-700 rounded-xl bg-gradient-to-r from-gray-50 to-gray-100 dark:from-gray-800/50 dark:to-gray-800/30 hover:shadow-lg transition-all duration-300",children:[(0,a.jsx)("div",{className:"w-32 h-32 rounded-full ".concat(l.bgColor," flex items-center justify-center text-6xl shadow-xl cursor-pointer hover:shadow-2xl hover:scale-105 transition-all duration-300 border-4 border-white dark:border-gray-700"),onClick:()=>o(!0),children:l.emoji}),(0,a.jsxs)("div",{className:"flex-1 space-y-3",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"font-bold text-2xl text-gray-900 dark:text-gray-100 mb-1",children:l.name}),(0,a.jsx)("div",{className:"text-base text-gray-600 dark:text-gray-400 mb-2 leading-relaxed",children:l.description}),(0,a.jsx)("div",{className:"inline-flex items-center text-sm text-primary font-semibold bg-primary/10 px-3 py-1 rounded-full",children:l.category})]}),(0,a.jsxs)("button",{onClick:()=>o(!0),className:"inline-flex items-center px-6 py-3 text-base font-semibold text-white bg-primary hover:bg-primary/90 rounded-lg shadow-md hover:shadow-lg transition-all duration-200 transform hover:scale-105",children:[(0,a.jsx)(N,{className:"w-5 h-5 mr-2"}),"Change Avatar"]})]})]}),n&&(0,a.jsx)("div",{className:"fixed inset-0 z-50 flex items-center justify-center p-4 bg-black/50 backdrop-blur-sm",children:(0,a.jsxs)("div",{className:"bg-white dark:bg-gray-900 rounded-xl shadow-2xl max-w-4xl w-full max-h-[90vh] overflow-hidden",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h2",{className:"text-xl font-semibold text-gray-900 dark:text-gray-100",children:"Choose Your Trading Avatar"}),(0,a.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-400 mt-1",children:"Select an avatar that represents your trading style"})]}),(0,a.jsx)("button",{onClick:()=>o(!1),className:"p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors",children:(0,a.jsx)(w.A,{className:"w-5 h-5"})})]}),(0,a.jsx)("div",{className:"p-6 overflow-y-auto max-h-[calc(90vh-120px)]",children:Object.entries(c).map(e=>{let[t,s]=e;return(0,a.jsxs)("div",{className:"mb-10 last:mb-0",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-gray-100 mb-6 border-b border-gray-200 dark:border-gray-700 pb-3",children:t}),(0,a.jsx)("div",{className:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-8",children:s.map(e=>(0,a.jsx)(C,{content:(0,a.jsxs)("div",{className:"text-center max-w-xs",children:[(0,a.jsx)("div",{className:"font-semibold text-white mb-1",children:e.name}),(0,a.jsx)("div",{className:"text-gray-200 text-xs mb-2",children:e.description}),(0,a.jsxs)("div",{className:"text-primary-200 text-xs font-medium",children:["Category: ",e.category]})]}),position:"top",delay:500,children:(0,a.jsxs)("div",{className:"relative cursor-pointer group p-8 rounded-2xl border-2 transition-all duration-300 hover:shadow-2xl hover:-translate-y-2 ".concat(r===e.id?"border-primary bg-primary/15 shadow-xl scale-105 ring-2 ring-primary/30":"border-gray-200 dark:border-gray-700 hover:border-primary/60 bg-white dark:bg-gray-800/50 hover:bg-gray-50 dark:hover:bg-gray-800/70"),onClick:()=>d(e.id),children:[(0,a.jsxs)("div",{className:"flex flex-col items-center text-center",children:[(0,a.jsx)("div",{className:"w-28 h-28 rounded-full ".concat(e.bgColor," flex items-center justify-center text-5xl shadow-xl group-hover:shadow-2xl transition-all duration-300 group-hover:scale-110 mb-6 border-4 border-white dark:border-gray-700 group-hover:border-primary/30"),children:e.emoji}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsx)("div",{className:"font-bold text-lg text-gray-900 dark:text-gray-100 group-hover:text-primary transition-colors",children:e.name}),(0,a.jsx)("div",{className:"text-sm text-gray-600 dark:text-gray-400 leading-relaxed px-2",children:e.description}),(0,a.jsx)("div",{className:"inline-flex items-center text-xs text-primary font-semibold bg-primary/15 px-3 py-1.5 rounded-full border border-primary/20",children:e.category})]})]}),r===e.id&&(0,a.jsx)("div",{className:"absolute -top-3 -right-3 w-10 h-10 bg-primary rounded-full flex items-center justify-center shadow-xl border-3 border-white dark:border-gray-900 animate-pulse",children:(0,a.jsx)(S.A,{className:"w-6 h-6 text-white"})}),(0,a.jsx)("div",{className:"absolute inset-0 bg-gradient-to-t from-primary/5 to-transparent rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none"})]})},e.id))})]},t)})}),(0,a.jsxs)("div",{className:"flex items-center justify-between p-6 border-t border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-800/50",children:[(0,a.jsxs)("div",{className:"text-sm text-gray-600 dark:text-gray-400",children:[A.length," professional trading avatars available"]}),(0,a.jsxs)("div",{className:"flex space-x-3",children:[(0,a.jsx)("button",{onClick:()=>o(!1),className:"px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-md hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors",children:"Cancel"}),(0,a.jsx)("button",{onClick:()=>o(!1),className:"px-4 py-2 text-sm font-medium text-white bg-primary hover:bg-primary/90 rounded-md transition-colors",children:"Done"})]})]})]})})]})}function M(){let e=(0,i.useSearchParams)(),[r,t]=(0,s.useState)("account"),{theme:n,setTheme:N,colorScheme:w,setColorScheme:S}=(0,b.D)(),{user:C}=function(){let e=(0,i.useRouter)(),[r,t]=(0,s.useState)(null),[a,n]=(0,s.useState)(!0);return(0,s.useEffect)(()=>{let e=(0,v.hg)(j.j2,e=>{t(e),n(!1)});return()=>e()},[]),{user:r,loading:a,isAuthenticated:!!r,login:async(e,r)=>{try{return await (0,k.Ru)(e,r)}catch(e){throw console.error("Login error:",e),e}},loginWithGoogle:async()=>{try{return await (0,k.G6)()}catch(e){throw console.error("Google login error:",e),e}},logout:async()=>{try{await (0,k.signOutUser)(),e.push("/login")}catch(e){throw console.error("Logout error:",e),e}}}}(),[A,M]=(0,s.useState)(null),[E,T]=(0,s.useState)(!0),[U,_]=(0,s.useState)(!1),[F,L]=(0,s.useState)({name:"",email:"",phone:"",country:"us",bio:"",avatar:"bull-trader"}),[R,z]=(0,s.useState)({email:!0,push:!0,trading_alerts:!0,price_alerts:!0,news_updates:!1}),[I,D]=(0,s.useState)({default_leverage:10,risk_management:!0,auto_close_positions:!1,confirmation_dialogs:!0});(0,s.useEffect)(()=>{let r=e.get("tab");r&&["account","notifications","appearance","language","payment","security"].includes(r)&&t(r)},[e]),(0,s.useEffect)(()=>{let e=y.A.subscribe(e=>{console.log("Settings page - User data received:",e),M(e),e&&(L({name:e.name||"",email:e.email||"",phone:e.phone||"",country:e.country||"us",bio:e.bio||"",avatar:e.avatar||"bull-trader"}),e.settings&&(z(e.settings.notifications||R),D(e.settings.trading||I))),T(!1)});(()=>{let e=y.A.getFirebaseUser(),r=y.A.getUser();console.log("Settings page - Auth check:",{firebaseUser:e,currentUser:r}),e||r||T(!1)})();let r=setTimeout(()=>{console.log("Settings page - Timeout reached, stopping loading"),T(!1)},5e3);return()=>{e(),clearTimeout(r)}},[]);let G=async()=>{if(!C){alert("Please sign in to save settings.");return}if(!F.name.trim()){alert("Name is required.");return}_(!0);try{console.log("Saving account settings for user:",C.uid),console.log("Data to save:",{name:F.name,phone:F.phone,country:F.country,bio:F.bio,avatar:F.avatar}),await f.b.updateUserProfile(C.uid,{name:F.name.trim(),phone:F.phone.trim(),country:F.country,bio:F.bio.trim(),avatar:F.avatar}),console.log("Account settings saved successfully"),alert("Account settings saved successfully!")}catch(e){console.error("Error saving account settings:",e),alert("Failed to save account settings: ".concat(e.message||"Unknown error",". Please try again."))}finally{_(!1)}},B=async()=>{if(C){_(!0);try{await f.b.updateUserProfile(C.uid,{settings:{...null==A?void 0:A.settings,notifications:R}}),alert("Notification settings saved successfully!")}catch(e){console.error("Error saving notification settings:",e),alert("Failed to save notification settings. Please try again.")}finally{_(!1)}}},H=async()=>{if(C){_(!0);try{await f.b.updateUserProfile(C.uid,{settings:{...null==A?void 0:A.settings,trading:I}}),alert("Trading settings saved successfully!")}catch(e){console.error("Error saving trading settings:",e),alert("Failed to save trading settings. Please try again.")}finally{_(!1)}}},q=(e,r)=>{L(t=>({...t,[e]:r}))};return E?(0,a.jsx)("div",{className:"p-4 flex items-center justify-center min-h-[400px]",children:(0,a.jsxs)("div",{className:"flex flex-col items-center space-y-4",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(o,{className:"h-6 w-6 animate-spin"}),(0,a.jsx)("span",{children:"Loading settings..."})]}),(0,a.jsx)("div",{className:"text-sm text-gray-500",children:"If this takes too long, please try refreshing the page"})]})}):E||A||C?(0,a.jsxs)("div",{className:"p-3 sm:p-4",children:[(0,a.jsx)("div",{className:"flex flex-col md:flex-row md:items-center justify-between mb-4 sm:mb-6",children:(0,a.jsxs)("div",{className:"flex items-center mb-3 md:mb-0",children:[(0,a.jsx)("div",{className:"bg-emerald-100 rounded-full p-2 mr-3 dark:bg-emerald-900",children:(0,a.jsx)(l.A,{className:"h-5 w-5 sm:h-6 sm:w-6 text-emerald-600 dark:text-emerald-400"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-xl sm:text-2xl font-bold",children:"Settings"}),(0,a.jsx)("p",{className:"text-gray-500 text-xs sm:text-sm dark:text-gray-400",children:"Manage your account preferences"})]})]})}),(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-4 gap-4 sm:gap-6",children:[(0,a.jsxs)("div",{className:"lg:col-span-1",children:[(0,a.jsx)("div",{className:"lg:hidden mb-4",children:(0,a.jsx)("div",{className:"flex overflow-x-auto pb-2 gap-2",children:[{id:"account",label:"Account",icon:d.A},{id:"notifications",label:"Notifications",icon:c.A},{id:"appearance",label:"Appearance",icon:m.A},{id:"language",label:"Language",icon:u.A},{id:"payment",label:"Payment",icon:g.A},{id:"security",label:"Security",icon:h.A}].map(e=>{let s=e.icon;return(0,a.jsxs)("button",{onClick:()=>t(e.id),className:"flex flex-col items-center px-3 py-2 rounded-lg whitespace-nowrap text-xs font-medium ".concat(r===e.id?"bg-primary text-primary-foreground":"bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-700"),children:[(0,a.jsx)(s,{className:"h-4 w-4 mb-1"}),(0,a.jsx)("span",{className:"hidden sm:inline",children:e.label}),(0,a.jsx)("span",{className:"sm:hidden",children:e.label.slice(0,4)})]},e.id)})})}),(0,a.jsxs)("div",{className:"hidden lg:block bg-white rounded-lg border overflow-hidden dark:bg-gray-800 dark:border-gray-700",children:[(0,a.jsx)("div",{className:"p-4 border-b dark:border-gray-700",children:(0,a.jsx)("h2",{className:"font-medium",children:"Settings"})}),(0,a.jsxs)("nav",{className:"p-2",children:[(0,a.jsxs)("button",{onClick:()=>t("account"),className:"flex items-center w-full px-3 py-2 rounded-md text-left ".concat("account"===r?"bg-primary/10 text-primary":"hover:bg-gray-50 dark:hover:bg-gray-700"),children:[(0,a.jsx)(d.A,{className:"h-4 w-4 mr-3"}),(0,a.jsx)("span",{children:"Account"})]}),(0,a.jsxs)("button",{onClick:()=>t("notifications"),className:"flex items-center w-full px-3 py-2 rounded-md text-left ".concat("notifications"===r?"bg-primary/10 text-primary":"hover:bg-gray-50 dark:hover:bg-gray-700"),children:[(0,a.jsx)(c.A,{className:"h-4 w-4 mr-3"}),(0,a.jsx)("span",{children:"Notifications"})]}),(0,a.jsxs)("button",{onClick:()=>t("appearance"),className:"flex items-center w-full px-3 py-2 rounded-md text-left ".concat("appearance"===r?"bg-primary/10 text-primary":"hover:bg-gray-50 dark:hover:bg-gray-700"),children:[(0,a.jsx)(m.A,{className:"h-4 w-4 mr-3"}),(0,a.jsx)("span",{children:"Appearance"})]}),(0,a.jsxs)("button",{onClick:()=>t("language"),className:"flex items-center w-full px-3 py-2 rounded-md text-left ".concat("language"===r?"bg-primary/10 text-primary":"hover:bg-gray-50 dark:hover:bg-gray-700"),children:[(0,a.jsx)(u.A,{className:"h-4 w-4 mr-3"}),(0,a.jsx)("span",{children:"Language & Region"})]}),(0,a.jsxs)("button",{onClick:()=>t("payment"),className:"flex items-center w-full px-3 py-2 rounded-md text-left ".concat("payment"===r?"bg-primary/10 text-primary":"hover:bg-gray-50 dark:hover:bg-gray-700"),children:[(0,a.jsx)(g.A,{className:"h-4 w-4 mr-3"}),(0,a.jsx)("span",{children:"Payment Methods"})]}),(0,a.jsxs)("button",{onClick:()=>t("security"),className:"flex items-center w-full px-3 py-2 rounded-md text-left ".concat("security"===r?"bg-primary/10 text-primary":"hover:bg-gray-50 dark:hover:bg-gray-700"),children:[(0,a.jsx)(h.A,{className:"h-4 w-4 mr-3"}),(0,a.jsx)("span",{children:"Security"})]})]})]})]}),(0,a.jsx)("div",{className:"lg:col-span-3",children:(0,a.jsxs)("div",{className:"bg-white rounded-lg border p-4 sm:p-6 dark:bg-gray-800 dark:border-gray-700",children:["account"===r&&(0,a.jsxs)("div",{children:[(0,a.jsx)("h2",{className:"text-base sm:text-lg font-bold mb-4 sm:mb-6",children:"Account Settings"}),(0,a.jsxs)("div",{className:"space-y-4 sm:space-y-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-xs sm:text-sm font-medium text-gray-700 dark:text-gray-300 mb-2 sm:mb-3",children:"Profile Picture"}),(0,a.jsx)(P,{selectedAvatar:F.avatar,onSelect:e=>q("avatar",e)})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4 sm:gap-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"fullName",className:"block text-xs sm:text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:"Full Name"}),(0,a.jsx)("input",{type:"text",id:"fullName",value:F.name,onChange:e=>q("name",e.target.value),className:"w-full border border-gray-300 rounded-md px-3 py-2 text-sm sm:text-base focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary dark:bg-gray-700 dark:border-gray-600 dark:text-gray-200"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"email",className:"block text-xs sm:text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:"Email Address"}),(0,a.jsx)("input",{type:"email",id:"email",value:F.email,disabled:!0,className:"w-full border border-gray-300 rounded-md px-3 py-2 text-sm sm:text-base bg-gray-50 text-gray-500 cursor-not-allowed dark:bg-gray-600 dark:border-gray-600 dark:text-gray-400",title:"Email cannot be changed"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"phone",className:"block text-xs sm:text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:"Phone Number"}),(0,a.jsx)("input",{type:"tel",id:"phone",value:F.phone,onChange:e=>q("phone",e.target.value),placeholder:"+****************",className:"w-full border border-gray-300 rounded-md px-3 py-2 text-sm sm:text-base focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary dark:bg-gray-700 dark:border-gray-600 dark:text-gray-200"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"country",className:"block text-xs sm:text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:"Country"}),(0,a.jsxs)("select",{id:"country",value:F.country,onChange:e=>q("country",e.target.value),className:"w-full border border-gray-300 rounded-md px-3 py-2 text-sm sm:text-base focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary dark:bg-gray-700 dark:border-gray-600 dark:text-gray-200",children:[(0,a.jsx)("option",{value:"us",children:"United States"}),(0,a.jsx)("option",{value:"ca",children:"Canada"}),(0,a.jsx)("option",{value:"uk",children:"United Kingdom"}),(0,a.jsx)("option",{value:"au",children:"Australia"}),(0,a.jsx)("option",{value:"de",children:"Germany"}),(0,a.jsx)("option",{value:"fr",children:"France"}),(0,a.jsx)("option",{value:"jp",children:"Japan"}),(0,a.jsx)("option",{value:"kr",children:"South Korea"}),(0,a.jsx)("option",{value:"sg",children:"Singapore"}),(0,a.jsx)("option",{value:"hk",children:"Hong Kong"})]})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"bio",className:"block text-xs sm:text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:"Bio"}),(0,a.jsx)("textarea",{id:"bio",rows:3,value:F.bio,onChange:e=>q("bio",e.target.value),placeholder:"Tell us about yourself...",className:"w-full border border-gray-300 rounded-md px-3 py-2 text-sm sm:text-base focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary dark:bg-gray-700 dark:border-gray-600 dark:text-gray-200"})]}),(0,a.jsx)("div",{className:"pt-3 sm:pt-4 border-t dark:border-gray-700 flex justify-end",children:(0,a.jsxs)("button",{onClick:G,disabled:U,className:"bg-primary text-primary-foreground px-3 sm:px-4 py-2 rounded-md text-xs sm:text-sm font-medium hover:bg-primary/90 flex items-center disabled:opacity-50 disabled:cursor-not-allowed",children:[U?(0,a.jsx)(o,{className:"h-3 w-3 sm:h-4 sm:w-4 mr-1 sm:mr-2 animate-spin"}):(0,a.jsx)(x,{className:"h-3 w-3 sm:h-4 sm:w-4 mr-1 sm:mr-2"}),U?"Saving...":"Save Changes"]})})]})]}),"notifications"===r&&(0,a.jsxs)("div",{children:[(0,a.jsx)("h2",{className:"text-base sm:text-lg font-bold mb-4 sm:mb-6",children:"Notification Settings"}),(0,a.jsxs)("div",{className:"space-y-4 sm:space-y-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-sm sm:text-base font-medium mb-2 sm:mb-3",children:"Email Notifications"}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("div",{className:"flex items-start sm:items-center justify-between gap-3",children:[(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsx)("div",{className:"text-sm sm:text-base font-medium",children:"Price Alerts"}),(0,a.jsx)("div",{className:"text-xs sm:text-sm text-gray-500 dark:text-gray-400",children:"Get notified when prices change significantly"})]}),(0,a.jsxs)("label",{className:"relative inline-flex items-center cursor-pointer",children:[(0,a.jsx)("input",{type:"checkbox",checked:R.price_alerts,onChange:()=>z({...R,price_alerts:!R.price_alerts}),className:"sr-only peer"}),(0,a.jsx)("div",{className:"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-primary/30 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary dark:bg-gray-700"})]})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"font-medium",children:"Account Activity"}),(0,a.jsx)("div",{className:"text-sm text-gray-500 dark:text-gray-400",children:"Get notified about sign-ins and security alerts"})]}),(0,a.jsxs)("label",{className:"relative inline-flex items-center cursor-pointer",children:[(0,a.jsx)("input",{type:"checkbox",checked:R.email,onChange:()=>z({...R,email:!R.email}),className:"sr-only peer"}),(0,a.jsx)("div",{className:"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-primary/30 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary dark:bg-gray-700"})]})]})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"font-medium mb-3",children:"Push Notifications"}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"font-medium",children:"Price Alerts"}),(0,a.jsx)("div",{className:"text-sm text-gray-500 dark:text-gray-400",children:"Get notified when prices change significantly"})]}),(0,a.jsxs)("label",{className:"relative inline-flex items-center cursor-pointer",children:[(0,a.jsx)("input",{type:"checkbox",checked:R.push,onChange:()=>z({...R,push:!R.push}),className:"sr-only peer"}),(0,a.jsx)("div",{className:"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-primary/30 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary dark:bg-gray-700"})]})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"font-medium",children:"Trading Activity"}),(0,a.jsx)("div",{className:"text-sm text-gray-500 dark:text-gray-400",children:"Get notified about your trades and orders"})]}),(0,a.jsxs)("label",{className:"relative inline-flex items-center cursor-pointer",children:[(0,a.jsx)("input",{type:"checkbox",checked:R.trading_alerts,onChange:()=>z({...R,trading_alerts:!R.trading_alerts}),className:"sr-only peer"}),(0,a.jsx)("div",{className:"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-primary/30 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary dark:bg-gray-700"})]})]})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"font-medium mb-3",children:"SMS Notifications"}),(0,a.jsx)("div",{className:"space-y-3",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"font-medium",children:"Security Alerts"}),(0,a.jsx)("div",{className:"text-sm text-gray-500 dark:text-gray-400",children:"Get notified about important security events"})]}),(0,a.jsxs)("label",{className:"relative inline-flex items-center cursor-pointer",children:[(0,a.jsx)("input",{type:"checkbox",checked:R.news_updates,onChange:()=>z({...R,news_updates:!R.news_updates}),className:"sr-only peer"}),(0,a.jsx)("div",{className:"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-primary/30 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary dark:bg-gray-700"})]})]})})]}),(0,a.jsx)("div",{className:"pt-4 border-t dark:border-gray-700 flex justify-end",children:(0,a.jsxs)("button",{onClick:B,disabled:U,className:"bg-primary text-primary-foreground px-4 py-2 rounded-md font-medium hover:bg-primary/90 flex items-center disabled:opacity-50 disabled:cursor-not-allowed",children:[U?(0,a.jsx)(o,{className:"h-4 w-4 mr-2 animate-spin"}):(0,a.jsx)(x,{className:"h-4 w-4 mr-2"}),U?"Saving...":"Save Changes"]})})]})]}),"appearance"===r&&(0,a.jsxs)("div",{children:[(0,a.jsx)("h2",{className:"text-base sm:text-lg font-bold mb-4 sm:mb-6",children:"Appearance Settings"}),(0,a.jsxs)("div",{className:"space-y-4 sm:space-y-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-sm sm:text-base font-medium mb-2 sm:mb-3",children:"Theme"}),(0,a.jsxs)("div",{className:"grid grid-cols-1 sm:grid-cols-3 gap-3 sm:gap-4",children:[(0,a.jsxs)("div",{className:"border rounded-lg p-3 sm:p-4 cursor-pointer dark:border-gray-700 ".concat("light"===n?"ring-2 ring-primary":""),onClick:()=>N("light"),children:[(0,a.jsxs)("div",{className:"flex justify-between items-center mb-2 sm:mb-3",children:[(0,a.jsx)("div",{className:"text-sm sm:text-base font-medium",children:"Light"}),(0,a.jsx)(p.A,{className:"h-4 w-4 sm:h-5 sm:w-5 text-yellow-500"})]}),(0,a.jsx)("div",{className:"h-16 sm:h-20 bg-white border rounded-md dark:border-gray-700"})]}),(0,a.jsxs)("div",{className:"border rounded-lg p-3 sm:p-4 cursor-pointer dark:border-gray-700 ".concat("dark"===n?"ring-2 ring-primary":""),onClick:()=>N("dark"),children:[(0,a.jsxs)("div",{className:"flex justify-between items-center mb-2 sm:mb-3",children:[(0,a.jsx)("div",{className:"text-sm sm:text-base font-medium",children:"Dark"}),(0,a.jsx)(m.A,{className:"h-4 w-4 sm:h-5 sm:w-5 text-gray-700 dark:text-gray-300"})]}),(0,a.jsx)("div",{className:"h-16 sm:h-20 bg-gray-900 rounded-md"})]}),(0,a.jsxs)("div",{className:"border rounded-lg p-3 sm:p-4 cursor-pointer dark:border-gray-700 ".concat("system"===n?"ring-2 ring-primary":""),onClick:()=>N("system"),children:[(0,a.jsxs)("div",{className:"flex justify-between items-center mb-2 sm:mb-3",children:[(0,a.jsx)("div",{className:"text-sm sm:text-base font-medium",children:"System"}),(0,a.jsxs)("div",{className:"flex",children:[(0,a.jsx)(p.A,{className:"h-4 w-4 sm:h-5 sm:w-5 text-yellow-500"}),(0,a.jsx)("span",{className:"mx-1 text-xs sm:text-sm",children:"/"}),(0,a.jsx)(m.A,{className:"h-4 w-4 sm:h-5 sm:w-5 text-gray-700 dark:text-gray-300"})]})]}),(0,a.jsx)("div",{className:"h-16 sm:h-20 bg-gradient-to-r from-white to-gray-900 rounded-md"})]})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-sm sm:text-base font-medium mb-2 sm:mb-3",children:"Color Scheme"}),(0,a.jsxs)("div",{className:"grid grid-cols-2 sm:grid-cols-4 gap-3 sm:gap-4",children:[(0,a.jsxs)("div",{className:"border rounded-lg p-2 cursor-pointer dark:border-gray-700 ".concat("emerald"===w?"ring-2 ring-primary":""),onClick:()=>S("emerald"),children:[(0,a.jsx)("div",{className:"h-8 sm:h-10 bg-emerald-500 rounded-md"}),(0,a.jsx)("div",{className:"text-center mt-1 sm:mt-2 text-xs sm:text-sm font-medium",children:"Emerald"})]}),(0,a.jsxs)("div",{className:"border rounded-lg p-2 cursor-pointer dark:border-gray-700 ".concat("blue"===w?"ring-2 ring-primary":""),onClick:()=>S("blue"),children:[(0,a.jsx)("div",{className:"h-8 sm:h-10 bg-blue-500 rounded-md"}),(0,a.jsx)("div",{className:"text-center mt-1 sm:mt-2 text-xs sm:text-sm font-medium",children:"Blue"})]}),(0,a.jsxs)("div",{className:"border rounded-lg p-2 cursor-pointer dark:border-gray-700 ".concat("purple"===w?"ring-2 ring-primary":""),onClick:()=>S("purple"),children:[(0,a.jsx)("div",{className:"h-8 sm:h-10 bg-purple-500 rounded-md"}),(0,a.jsx)("div",{className:"text-center mt-1 sm:mt-2 text-xs sm:text-sm font-medium",children:"Purple"})]}),(0,a.jsxs)("div",{className:"border rounded-lg p-2 cursor-pointer dark:border-gray-700 ".concat("orange"===w?"ring-2 ring-primary":""),onClick:()=>S("orange"),children:[(0,a.jsx)("div",{className:"h-8 sm:h-10 bg-orange-500 rounded-md"}),(0,a.jsx)("div",{className:"text-center mt-1 sm:mt-2 text-xs sm:text-sm font-medium",children:"Orange"})]})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"font-medium mb-3",children:"Trading Preferences"}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:"Default Leverage"}),(0,a.jsxs)("select",{value:I.default_leverage,onChange:e=>D({...I,default_leverage:parseInt(e.target.value)}),className:"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary dark:bg-gray-700 dark:border-gray-600 dark:text-gray-200",children:[(0,a.jsx)("option",{value:1,children:"1x"}),(0,a.jsx)("option",{value:2,children:"2x"}),(0,a.jsx)("option",{value:5,children:"5x"}),(0,a.jsx)("option",{value:10,children:"10x"}),(0,a.jsx)("option",{value:20,children:"20x"}),(0,a.jsx)("option",{value:50,children:"50x"}),(0,a.jsx)("option",{value:100,children:"100x"})]})]}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"font-medium",children:"Risk Management"}),(0,a.jsx)("div",{className:"text-sm text-gray-500 dark:text-gray-400",children:"Enable automatic risk management features"})]}),(0,a.jsxs)("label",{className:"relative inline-flex items-center cursor-pointer",children:[(0,a.jsx)("input",{type:"checkbox",checked:I.risk_management,onChange:()=>D({...I,risk_management:!I.risk_management}),className:"sr-only peer"}),(0,a.jsx)("div",{className:"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-primary/30 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary dark:bg-gray-700"})]})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"font-medium",children:"Confirmation Dialogs"}),(0,a.jsx)("div",{className:"text-sm text-gray-500 dark:text-gray-400",children:"Show confirmation before placing orders"})]}),(0,a.jsxs)("label",{className:"relative inline-flex items-center cursor-pointer",children:[(0,a.jsx)("input",{type:"checkbox",checked:I.confirmation_dialogs,onChange:()=>D({...I,confirmation_dialogs:!I.confirmation_dialogs}),className:"sr-only peer"}),(0,a.jsx)("div",{className:"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-primary/30 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary dark:bg-gray-700"})]})]})]})]})]}),(0,a.jsx)("div",{className:"pt-4 border-t dark:border-gray-700 flex justify-end",children:(0,a.jsxs)("button",{onClick:H,disabled:U,className:"bg-primary text-primary-foreground px-4 py-2 rounded-md font-medium hover:bg-primary/90 flex items-center disabled:opacity-50 disabled:cursor-not-allowed",children:[U?(0,a.jsx)(o,{className:"h-4 w-4 mr-2 animate-spin"}):(0,a.jsx)(x,{className:"h-4 w-4 mr-2"}),U?"Saving...":"Save Changes"]})})]})]}),"language"===r&&(0,a.jsxs)("div",{children:[(0,a.jsx)("h2",{className:"text-lg font-bold mb-6",children:"Language & Region Settings"}),(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"language",className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:"Language"}),(0,a.jsxs)("select",{id:"language",value:language,onChange:e=>setLanguage(e.target.value),className:"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary dark:bg-gray-700 dark:border-gray-600 dark:text-gray-200",children:[(0,a.jsx)("option",{value:"english",children:"English"}),(0,a.jsx)("option",{value:"spanish",children:"Spanish"}),(0,a.jsx)("option",{value:"french",children:"French"}),(0,a.jsx)("option",{value:"german",children:"German"}),(0,a.jsx)("option",{value:"chinese",children:"Chinese"}),(0,a.jsx)("option",{value:"japanese",children:"Japanese"}),(0,a.jsx)("option",{value:"korean",children:"Korean"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"currency",className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:"Currency"}),(0,a.jsxs)("select",{id:"currency",value:currency,onChange:e=>setCurrency(e.target.value),className:"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary dark:bg-gray-700 dark:border-gray-600 dark:text-gray-200",children:[(0,a.jsx)("option",{value:"usd",children:"USD - US Dollar"}),(0,a.jsx)("option",{value:"eur",children:"EUR - Euro"}),(0,a.jsx)("option",{value:"gbp",children:"GBP - British Pound"}),(0,a.jsx)("option",{value:"jpy",children:"JPY - Japanese Yen"}),(0,a.jsx)("option",{value:"cad",children:"CAD - Canadian Dollar"}),(0,a.jsx)("option",{value:"aud",children:"AUD - Australian Dollar"})]})]}),(0,a.jsx)("div",{className:"pt-4 border-t dark:border-gray-700 flex justify-end",children:(0,a.jsxs)("button",{className:"bg-primary text-primary-foreground px-4 py-2 rounded-md font-medium hover:bg-primary/90 flex items-center",children:[(0,a.jsx)(x,{className:"h-4 w-4 mr-2"}),"Save Changes"]})})]})]}),"payment"===r&&(0,a.jsxs)("div",{children:[(0,a.jsx)("h2",{className:"text-lg font-bold mb-6",children:"Payment Methods"}),(0,a.jsx)("div",{className:"pt-4 border-t dark:border-gray-700 flex justify-end",children:(0,a.jsxs)("button",{className:"bg-primary text-primary-foreground px-4 py-2 rounded-md font-medium hover:bg-primary/90 flex items-center",children:[(0,a.jsx)(x,{className:"h-4 w-4 mr-2"}),"Save Changes"]})})]}),"security"===r&&(0,a.jsxs)("div",{children:[(0,a.jsx)("h2",{className:"text-lg font-bold mb-6",children:"Security Settings"}),(0,a.jsx)("div",{className:"pt-4 border-t dark:border-gray-700 flex justify-end",children:(0,a.jsxs)("button",{className:"bg-primary text-primary-foreground px-4 py-2 rounded-md font-medium hover:bg-primary/90 flex items-center",children:[(0,a.jsx)(x,{className:"h-4 w-4 mr-2"}),"Save Changes"]})})]})]})})]})]}):(0,a.jsx)("div",{className:"p-4 flex items-center justify-center min-h-[400px]",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"text-lg font-medium mb-2",children:"Please sign in to access settings"}),(0,a.jsx)("div",{className:"text-gray-500 mb-4",children:"You need to be authenticated to view and modify your settings."}),(0,a.jsx)("button",{onClick:()=>window.location.href="/login",className:"bg-primary text-primary-foreground px-4 py-2 rounded-md font-medium hover:bg-primary/90",children:"Go to Sign In"})]})})}},54416:(e,r,t)=>{"use strict";t.d(r,{A:()=>a});let a=(0,t(19946).A)("X",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},62098:(e,r,t)=>{"use strict";t.d(r,{A:()=>a});let a=(0,t(19946).A)("Sun",[["circle",{cx:"12",cy:"12",r:"4",key:"4exip2"}],["path",{d:"M12 2v2",key:"tus03m"}],["path",{d:"M12 20v2",key:"1lh1kg"}],["path",{d:"m4.93 4.93 1.41 1.41",key:"149t6j"}],["path",{d:"m17.66 17.66 1.41 1.41",key:"ptbguv"}],["path",{d:"M2 12h2",key:"1t8f8n"}],["path",{d:"M20 12h2",key:"1q8mjw"}],["path",{d:"m6.34 17.66-1.41 1.41",key:"1m8zz5"}],["path",{d:"m19.07 4.93-1.41 1.41",key:"1shlcs"}]])},64764:(e,r,t)=>{"use strict";t.d(r,{A:()=>n});var a=t(91317),s=t(50475);class i{subscribe(e){return this.subscribers.push(e),this.isInitialized&&e(this.preferences),()=>{let r=this.subscribers.indexOf(e);r>-1&&this.subscribers.splice(r,1)}}notifySubscribers(){this.subscribers.forEach(e=>e(this.preferences))}async loadUserPreferences(e){try{let r=await a.b.getUserProfile(e);(null==r?void 0:r.preferences)&&(this.preferences={...this.preferences,...r.preferences}),this.isInitialized=!0,this.notifySubscribers()}catch(e){console.error("Error loading user preferences:",e),this.isInitialized=!0,this.notifySubscribers()}}resetPreferences(){this.preferences={favoriteMarkets:[],theme:"light",colorScheme:"emerald"},this.isInitialized=!1}getPreferences(){return{...this.preferences}}async updateFavoriteMarkets(e){let r=s.A.getFirebaseUser();if(r)try{this.preferences.favoriteMarkets=e,await a.b.updateUserProfile(r.uid,{preferences:this.preferences}),this.notifySubscribers()}catch(e){console.error("Error updating favorite markets:",e)}}async updateTheme(e){let r=s.A.getFirebaseUser();if(!r){this.preferences.theme=e,this.notifySubscribers();return}try{this.preferences.theme=e,await a.b.updateUserProfile(r.uid,{preferences:this.preferences}),this.notifySubscribers()}catch(e){console.error("Error updating theme:",e)}}async updateColorScheme(e){let r=s.A.getFirebaseUser();if(!r){this.preferences.colorScheme=e,this.notifySubscribers();return}try{this.preferences.colorScheme=e,await a.b.updateUserProfile(r.uid,{preferences:this.preferences}),this.notifySubscribers()}catch(e){console.error("Error updating color scheme:",e)}}async updateRememberedUsername(e){try{e?this.preferences.rememberedUsername=e:delete this.preferences.rememberedUsername,e?localStorage.setItem("rememberedUsername",e):localStorage.removeItem("rememberedUsername"),this.notifySubscribers()}catch(e){console.error("Error updating remembered username:",e)}}getRememberedUsername(){return this.preferences.rememberedUsername?this.preferences.rememberedUsername:localStorage.getItem("rememberedUsername")}async toggleFavoriteMarket(e){let r=this.preferences.favoriteMarkets,t=r.includes(e)?r.filter(r=>r!==e):[...r,e];await this.updateFavoriteMarkets(t)}isFavoriteMarket(e){return this.preferences.favoriteMarkets.includes(e)}getFavoriteMarkets(){return[...this.preferences.favoriteMarkets]}getTheme(){return this.preferences.theme}getColorScheme(){return this.preferences.colorScheme}constructor(){this.preferences={favoriteMarkets:[],theme:"light",colorScheme:"emerald"},this.subscribers=[],this.isInitialized=!1,s.A.subscribe(e=>{e?this.loadUserPreferences(e.id):this.resetPreferences()})}}let n=new i},71007:(e,r,t)=>{"use strict";t.d(r,{A:()=>a});let a=(0,t(19946).A)("User",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},75525:(e,r,t)=>{"use strict";t.d(r,{A:()=>a});let a=(0,t(19946).A)("Shield",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}]])},81586:(e,r,t)=>{"use strict";t.d(r,{A:()=>a});let a=(0,t(19946).A)("CreditCard",[["rect",{width:"20",height:"14",x:"2",y:"5",rx:"2",key:"ynyp8z"}],["line",{x1:"2",x2:"22",y1:"10",y2:"10",key:"1b3vmo"}]])},86617:(e,r,t)=>{Promise.resolve().then(t.bind(t,36443))},93509:(e,r,t)=>{"use strict";t.d(r,{A:()=>a});let a=(0,t(19946).A)("Moon",[["path",{d:"M12 3a6 6 0 0 0 9 9 9 9 0 1 1-9-9Z",key:"a7tn18"}]])},97636:(e,r,t)=>{"use strict";t.d(r,{D:()=>l,N:()=>o});var a=t(95155),s=t(12115),i=t(64764);let n=(0,s.createContext)(void 0);function o(e){let{children:r}=e,[t,o]=(0,s.useState)("light"),[l,d]=(0,s.useState)("emerald"),[c,m]=(0,s.useState)("light"),[u,g]=(0,s.useState)(!1);return((0,s.useEffect)(()=>(g(!0),i.A.subscribe(e=>{o(e.theme),d(e.colorScheme)})),[]),(0,s.useEffect)(()=>{u&&("dark"===t||"system"===t&&window.matchMedia("(prefers-color-scheme: dark)").matches?(document.documentElement.classList.add("dark"),m("dark")):(document.documentElement.classList.remove("dark"),m("light")),document.documentElement.setAttribute("data-color-scheme",l))},[t,l,u]),(0,s.useEffect)(()=>{if(!u||"system"!==t)return;let e=window.matchMedia("(prefers-color-scheme: dark)"),r=()=>{e.matches?(document.documentElement.classList.add("dark"),m("dark")):(document.documentElement.classList.remove("dark"),m("light"))};return e.addEventListener("change",r),()=>e.removeEventListener("change",r)},[t,u]),u)?(0,a.jsx)(n.Provider,{value:{theme:t,setTheme:e=>{o(e),i.A.updateTheme(e)},colorScheme:l,setColorScheme:e=>{d(e),i.A.updateColorScheme(e)},resolvedTheme:c},children:r}):(0,a.jsx)(a.Fragment,{children:r})}function l(){let e=(0,s.useContext)(n);if(void 0===e)throw Error("useTheme must be used within a ThemeProvider");return e}}},e=>{var r=r=>e(e.s=r);e.O(0,[992,507,965,585,475,441,684,358],()=>r(86617)),_N_E=e.O()}]);