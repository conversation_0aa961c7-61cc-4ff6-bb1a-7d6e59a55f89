"use client"

import { useState } from "react"
import Link from "next/link"
import { usePathname } from "next/navigation"
import {
  BarChart3,
  Wallet,
  LineChart,
  MessageSquare,
  Settings,
  LogOut,
  Home,
  ChevronDown,
  ChevronUp,
} from "lucide-react"
import ThemeToggle from "@/components/theme-toggle"

interface SideNavigationProps {
  onCloseMobile?: () => void
}

export default function SideNavigation({ onCloseMobile }: SideNavigationProps) {
  const pathname = usePathname()
  const [openSection, setOpenSection] = useState<string | null>("trading")

  const toggleSection = (section: string) => {
    setOpenSection(openSection === section ? null : section)
  }

  const isActive = (path: string) => {
    return pathname === path
  }

  const handleLinkClick = () => {
    if (onCloseMobile) {
      onCloseMobile()
    }
  }

  return (
    <div className="h-full w-full flex flex-col bg-gradient-to-br from-card via-card/98 to-muted/20 text-card-foreground">
      <div className="flex-1 overflow-auto py-4">
        <nav className="px-4 space-y-3">
          {/* Main Navigation Label */}
          <div className="px-2 mb-4">
            <h3 className="text-2xs font-bold text-muted-foreground uppercase tracking-wider">Navigation</h3>
          </div>

          {/* Dashboard Section */}
          <div>
            <Link
              href="/trade"
              className={`group flex items-center px-4 py-3.5 text-sm rounded-2xl transition-all duration-300 ${
                isActive("/trade")
                  ? "bg-gradient-to-r from-primary via-primary/95 to-primary/80 text-primary-foreground font-semibold shadow-xl shadow-primary/30 scale-105"
                  : "text-foreground hover:bg-gradient-to-r hover:from-muted/60 hover:to-muted/40 hover:shadow-lg hover:scale-102"
              }`}
              onClick={handleLinkClick}
            >
              <div className={`mr-3 p-1.5 rounded-lg transition-all duration-300 ${
                isActive("/trade")
                  ? "bg-primary-foreground/20"
                  : "group-hover:bg-primary/10"
              }`}>
                <Home className="h-4 w-4" />
              </div>
              <span className="font-medium">Trade</span>
              {isActive("/trade") && (
                <div className="ml-auto w-2 h-2 bg-primary-foreground rounded-full animate-pulse"></div>
              )}
            </Link>
          </div>

          {/* Trading Section */}
          <div>
            <button
              className="group w-full flex items-center justify-between px-4 py-3.5 text-sm rounded-2xl text-foreground hover:bg-gradient-to-r hover:from-muted/60 hover:to-muted/40 hover:shadow-lg transition-all duration-300"
              onClick={() => toggleSection("trading")}
            >
              <div className="flex items-center">
                <div className="mr-3 p-1.5 rounded-lg group-hover:bg-primary/10 transition-all duration-300">
                  <LineChart className="h-4 w-4" />
                </div>
                <span className="font-medium">Trading</span>
              </div>
              {openSection === "trading" ? (
                <ChevronUp className="h-4 w-4 text-muted-foreground transition-transform duration-300 group-hover:text-foreground" />
              ) : (
                <ChevronDown className="h-4 w-4 text-muted-foreground transition-transform duration-300 group-hover:text-foreground" />
              )}
            </button>

            {openSection === "trading" && (
              <div className="mt-3 ml-8 space-y-2 animate-in slide-in-from-top-3 duration-300">
                <Link
                  href="/trade"
                  className={`group flex items-center justify-between px-4 py-3 text-sm rounded-xl transition-all duration-300 ${
                    isActive("/trade")
                      ? "bg-gradient-to-r from-primary/30 via-primary/25 to-primary/20 text-primary font-bold border border-primary/40 shadow-lg shadow-primary/20 scale-105"
                      : "text-foreground hover:text-primary hover:bg-gradient-to-r hover:from-primary/10 hover:to-primary/5 hover:border hover:border-primary/20 hover:shadow-md hover:scale-102 font-semibold"
                  }`}
                  onClick={handleLinkClick}
                >
                  <div className="flex items-center">
                    <div className={`mr-3 p-1.5 rounded-lg transition-all duration-300 ${
                      isActive("/trade")
                        ? "bg-primary/20 text-primary"
                        : "bg-primary/10 text-primary group-hover:bg-primary/15"
                    }`}>
                      <LineChart className="h-3.5 w-3.5" />
                    </div>
                    <span>Futures</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    {isActive("/trade") && (
                      <div className="w-2 h-2 bg-primary rounded-full animate-pulse"></div>
                    )}
                    <span className="text-xs bg-gradient-to-r from-emerald-500/20 to-emerald-600/20 text-emerald-600 dark:text-emerald-400 px-2.5 py-1 rounded-lg font-bold border border-emerald-500/30 shadow-sm">
                      LIVE
                    </span>
                  </div>
                </Link>
                <Link
                  href="#"
                  className="group flex items-center justify-between px-4 py-2.5 text-sm rounded-xl text-muted-foreground hover:text-foreground hover:bg-gradient-to-r hover:from-muted/40 hover:to-muted/20 transition-all duration-300"
                  onClick={handleLinkClick}
                >
                  <span>Margin</span>
                  <span className="text-xs bg-gradient-to-r from-amber-500/20 to-amber-600/20 text-amber-600 dark:text-amber-400 px-2.5 py-1 rounded-lg font-medium border border-amber-500/20">
                    Soon
                  </span>
                </Link>
                <Link
                  href="#"
                  className="group flex items-center justify-between px-4 py-2.5 text-sm rounded-xl text-muted-foreground hover:text-foreground hover:bg-gradient-to-r hover:from-muted/40 hover:to-muted/20 transition-all duration-300"
                  onClick={handleLinkClick}
                >
                  <span>Options</span>
                  <span className="text-xs bg-gradient-to-r from-amber-500/20 to-amber-600/20 text-amber-600 dark:text-amber-400 px-2.5 py-1 rounded-lg font-medium border border-amber-500/20">
                    Soon
                  </span>
                </Link>
              </div>
            )}
          </div>

          {/* Wallet Section */}
          <div>
            <Link
              href="/wallet"
              className={`group flex items-center px-4 py-3.5 text-sm rounded-2xl transition-all duration-300 ${
                isActive("/wallet")
                  ? "bg-gradient-to-r from-primary via-primary/95 to-primary/80 text-primary-foreground font-semibold shadow-xl shadow-primary/30 scale-105"
                  : "text-foreground hover:bg-gradient-to-r hover:from-muted/60 hover:to-muted/40 hover:shadow-lg hover:scale-102"
              }`}
              onClick={handleLinkClick}
            >
              <div className={`mr-3 p-1.5 rounded-lg transition-all duration-300 ${
                isActive("/wallet")
                  ? "bg-primary-foreground/20"
                  : "group-hover:bg-primary/10"
              }`}>
                <Wallet className="h-4 w-4" />
              </div>
              <span className="font-medium">Wallet</span>
              {isActive("/wallet") && (
                <div className="ml-auto w-2 h-2 bg-primary-foreground rounded-full animate-pulse"></div>
              )}
            </Link>
          </div>

          {/* Statistics Section */}
          <div>
            <Link
              href="/statistics"
              className={`group flex items-center px-4 py-3.5 text-sm rounded-2xl transition-all duration-300 ${
                isActive("/statistics")
                  ? "bg-gradient-to-r from-primary via-primary/95 to-primary/80 text-primary-foreground font-semibold shadow-xl shadow-primary/30 scale-105"
                  : "text-foreground hover:bg-gradient-to-r hover:from-muted/60 hover:to-muted/40 hover:shadow-lg hover:scale-102"
              }`}
              onClick={handleLinkClick}
            >
              <div className={`mr-3 p-1.5 rounded-lg transition-all duration-300 ${
                isActive("/statistics")
                  ? "bg-primary-foreground/20"
                  : "group-hover:bg-primary/10"
              }`}>
                <BarChart3 className="h-4 w-4" />
              </div>
              <span className="font-medium">Statistics</span>
              {isActive("/statistics") && (
                <div className="ml-auto w-2 h-2 bg-primary-foreground rounded-full animate-pulse"></div>
              )}
            </Link>
          </div>

          {/* AI Chat Section */}
          <div>
            <Link
              href="/chat"
              className={`group flex items-center px-4 py-3.5 text-sm rounded-2xl transition-all duration-300 ${
                isActive("/chat")
                  ? "bg-gradient-to-r from-primary via-primary/95 to-primary/80 text-primary-foreground font-semibold shadow-xl shadow-primary/30 scale-105"
                  : "text-foreground hover:bg-gradient-to-r hover:from-muted/60 hover:to-muted/40 hover:shadow-lg hover:scale-102"
              }`}
              onClick={handleLinkClick}
            >
              <div className={`mr-3 p-1.5 rounded-lg transition-all duration-300 ${
                isActive("/chat")
                  ? "bg-primary-foreground/20"
                  : "group-hover:bg-primary/10"
              }`}>
                <MessageSquare className="h-4 w-4" />
              </div>
              <span className="font-medium">AI Chat</span>
              {isActive("/chat") && (
                <div className="ml-auto w-2 h-2 bg-primary-foreground rounded-full animate-pulse"></div>
              )}
            </Link>
          </div>

          {/* Settings Section */}
          <div>
            <Link
              href="/settings"
              className={`group flex items-center px-4 py-3.5 text-sm rounded-2xl transition-all duration-300 ${
                isActive("/settings")
                  ? "bg-gradient-to-r from-primary via-primary/95 to-primary/80 text-primary-foreground font-semibold shadow-xl shadow-primary/30 scale-105"
                  : "text-foreground hover:bg-gradient-to-r hover:from-muted/60 hover:to-muted/40 hover:shadow-lg hover:scale-102"
              }`}
              onClick={handleLinkClick}
            >
              <div className={`mr-3 p-1.5 rounded-lg transition-all duration-300 ${
                isActive("/settings")
                  ? "bg-primary-foreground/20"
                  : "group-hover:bg-primary/10"
              }`}>
                <Settings className="h-4 w-4" />
              </div>
              <span className="font-medium">Settings</span>
              {isActive("/settings") && (
                <div className="ml-auto w-2 h-2 bg-primary-foreground rounded-full animate-pulse"></div>
              )}
            </Link>
          </div>
        </nav>
      </div>

      {/* Footer */}
      <div className="p-4 border-t border-border/30 bg-gradient-to-br from-muted/30 via-muted/20 to-background/50 backdrop-blur-sm">
        <div className="space-y-3">
          {/* Theme Toggle */}
          <div className="flex items-center justify-between p-3 rounded-2xl bg-gradient-to-r from-background/60 to-muted/40 border border-border/30">
            <div className="flex items-center space-x-2">
              <div className="w-2 h-2 bg-primary rounded-full animate-pulse"></div>
              <span className="text-responsive-xs font-semibold text-foreground">Theme</span>
            </div>
            <ThemeToggle />
          </div>

          {/* Beta Badge */}
          <div className="flex items-center justify-center">
            <div className="relative">
              <span className="bg-gradient-to-r from-primary/25 via-primary/20 to-primary/15 text-primary px-3 py-1.5 sm:px-4 sm:py-2 rounded-2xl text-2xs sm:text-xs font-bold border border-primary/30 shadow-lg shadow-primary/10">
                BETA VERSION
              </span>
              <div className="absolute -top-1 -right-1 w-3 h-3 bg-gradient-to-r from-emerald-400 to-emerald-500 rounded-full border-2 border-background animate-bounce"></div>
            </div>
          </div>

          {/* Logout Button */}
          <button
            onClick={async () => {
              try {
                // Use Firebase Auth signOut
                const { signOutUser } = await import('@/lib/auth-utils')
                await signOutUser()

                // Clear any remaining local storage
                localStorage.removeItem("rememberedUsername")

                // Redirect to login
                window.location.href = "/login"
              } catch (error) {
                console.error('Logout error:', error)
                // Fallback: redirect anyway
                window.location.href = "/login"
              }
            }}
            className="touch-target group flex items-center justify-center px-4 py-3.5 text-responsive-xs rounded-2xl bg-gradient-to-r from-red-500/15 via-red-500/10 to-red-600/15 text-red-600 dark:text-red-400 hover:from-red-500/25 hover:via-red-500/20 hover:to-red-600/25 border border-red-500/25 hover:border-red-500/40 transition-all duration-300 w-full shadow-lg hover:shadow-xl hover:scale-105"
          >
            <div className="mr-3 p-1 rounded-lg bg-red-500/20 group-hover:bg-red-500/30 transition-all duration-300">
              <LogOut className="h-4 w-4 group-hover:scale-110 transition-transform duration-300" />
            </div>
            <span className="font-semibold">Logout</span>
          </button>
        </div>
      </div>
    </div>
  )
}
