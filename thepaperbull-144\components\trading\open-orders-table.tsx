"use client"

import { X } from "lucide-react"
import { useTrading } from "../../contexts/trading-context"
import { toast } from "sonner"

export default function OpenOrdersTable() {
  const { orders, isLoading, cancelOrder } = useTrading()

  // Filter to show only open orders (exclude filled/executed orders)
  const openOrders = orders.filter(order => order.status === 'NEW')

  const handleCancelOrder = async (id: string) => {
    try {
      const order = orders.find(o => o.id === id)
      await cancelOrder(id)

      if (order) {
        toast.success("Order cancelled successfully", {
          description: `${order.symbol} ${order.side} order cancelled`
        })
      }
    } catch (err) {
      console.error("Error canceling order:", err)
      toast.error("Failed to cancel order. Please try again.")
    }
  }

  const formatTime = (timestamp: number) => {
    const date = new Date(timestamp)
    return date.toLocaleTimeString([], { hour: "2-digit", minute: "2-digit" })
  }

  if (isLoading) {
    return (
      <div className="bg-card rounded-lg border border-border p-3">
        <div className="flex justify-center py-6">
          <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary"></div>
        </div>
      </div>
    )
  }

  if (openOrders.length === 0) {
    return (
      <div className="bg-card rounded-lg border border-border p-3">
        <div className="text-center py-6 text-muted-foreground text-sm">No open orders</div>
      </div>
    )
  }

  return (
    <div className="bg-card rounded-lg border border-border p-3">
      <div className="overflow-x-auto">
        <table className="w-full text-xs">
          <thead>
            <tr className="border-b border-border">
              <th className="text-left py-2 font-medium text-muted-foreground">Symbol</th>
              <th className="text-left py-2 font-medium text-muted-foreground">Type</th>
              <th className="text-left py-2 font-medium text-muted-foreground">Side</th>
              <th className="text-left py-2 font-medium text-muted-foreground">Price</th>
              <th className="text-left py-2 font-medium text-muted-foreground">Amount</th>
              <th className="text-left py-2 font-medium text-muted-foreground">Leverage</th>
              <th className="text-left py-2 font-medium text-muted-foreground">SL/TP</th>
              <th className="text-left py-2 font-medium text-muted-foreground">Time</th>
              <th className="text-right py-2 font-medium text-muted-foreground">Actions</th>
            </tr>
          </thead>
          <tbody>
            {openOrders.map((order) => (
              <tr key={order.id} className="border-b border-border/50 hover:bg-muted/30">
                <td className="py-2">
                  <span className="font-medium">{order.symbol}</span>
                </td>
                <td className="py-2">{order.type}</td>
                <td className="py-2">
                  <span className={order.side === "BUY" ? "text-emerald-500" : "text-red-500"}>{order.side}</span>
                </td>
                <td className="py-2">{order.price.toFixed(2)}</td>
                <td className="py-2">{order.origQty.toFixed(4)}</td>
                <td className="py-2 font-medium">{order.leverage}x</td>
                <td className="py-2">
                  <div className="flex flex-col">
                    <div className="flex items-center">
                      <span className="text-red-500 mr-1">SL:</span>
                      <span>{order.stopLoss ? order.stopLoss.toFixed(2) : "-"}</span>
                    </div>
                    <div className="flex items-center">
                      <span className="text-emerald-500 mr-1">TP:</span>
                      <span>{order.takeProfit ? order.takeProfit.toFixed(2) : "-"}</span>
                    </div>
                  </div>
                </td>
                <td className="py-2">{formatTime(order.timestamp)}</td>
                <td className="py-2 text-right">
                  <button
                    onClick={() => handleCancelOrder(order.id)}
                    className="p-1 rounded-md hover:bg-muted"
                    title="Cancel Order"
                  >
                    <X className="h-3.5 w-3.5" />
                  </button>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  )
}
