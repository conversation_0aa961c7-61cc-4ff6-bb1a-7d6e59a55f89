"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[69],{17069:(e,t,i)=>{i.d(t,{rM:()=>b,fx:()=>g});var s=i(95155),r=i(12115),o=i(81115),a=i(98915),n=i(50475),c=i(27759);class l{async checkForExistingUser(){let e=0,t=async()=>{e++;let t=n.A.getUser(),i=n.A.getFirebaseUser();return(console.log("User check attempt ".concat(e,":"),{currentUser:t?{id:t.id,email:t.email}:null,firebaseUser:i?{uid:i.uid,email:i.email}:null,hasUserId:!!this.userId}),t&&t.id&&!this.userId)?(console.log("Found existing user on startup, initializing:",t.id),await this.initializeForUser(t.id),!0):!i||this.userId||t?!!this.userId:(console.log("Firebase user exists, waiting for user service to load..."),!1)};if(!await t()){for(let e=1;e<5;e++)if(await new Promise(t=>setTimeout(t,500*e)),await t())return;console.log("No user found after all attempts, service will initialize when user logs in")}}subscribe(e){return this.subscribers.push(e),e(this.state),()=>{let t=this.subscribers.indexOf(e);t>-1&&this.subscribers.splice(t,1)}}notifySubscribers(){this.subscribers.forEach(e=>e(this.state))}notifySubscribersDebounced(){this.updateTimeout&&clearTimeout(this.updateTimeout),this.updateTimeout=setTimeout(()=>{this.notifySubscribers()},100)}async initializeForUser(e){if(this.isInitializing&&this.initializationPromise)return console.log("Already initializing, waiting for completion..."),this.initializationPromise;this.isInitializing=!0,this.initializationPromise=this._doInitializeForUser(e);try{await this.initializationPromise}finally{this.isInitializing=!1,this.initializationPromise=null}}async _doInitializeForUser(e){console.log("Starting initialization for user:",e),this.userId&&this.userId!==e&&(console.log("Cleaning up previous user connections"),this.cleanup()),this.userId=e,this.state.isLoading=!0,this.state.connectionStatus="connecting",this.state.error=null,this.notifySubscribers();try{await this.initializeAccountInfo(),this.notifySubscribers(),await this.setupRealtimeListeners(),this.state.connectionStatus="connected",this.state.error=null,this.notifySubscribers(),console.log("Realtime trading service initialized successfully for user:",e)}catch(t){throw console.error("Error initializing realtime trading service:",t),this.state.isLoading=!1,this.state.connectionStatus="error",this.state.error="Failed to initialize trading service",this.notifySubscribers(),this.scheduleReconnect(e),t}}async reconnectForUser(e){console.log("Reconnecting for user:",e),this.reconnectTimeout&&(clearTimeout(this.reconnectTimeout),this.reconnectTimeout=null);try{await this.initializeForUser(e)}catch(e){console.error("Reconnection failed:",e)}}scheduleReconnect(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:5e3;this.reconnectTimeout&&clearTimeout(this.reconnectTimeout),console.log("Scheduling reconnect in ".concat(t,"ms for user:"),e),this.reconnectTimeout=setTimeout(()=>{this.userId===e&&(console.log("Attempting scheduled reconnect for user:",e),this.reconnectForUser(e))},t)}async initializeAccountInfo(){if(!this.userId)return;let e=n.A.getUserBalance();this.state.accountInfo={totalWalletBalance:e,totalUnrealizedProfit:0,totalMarginBalance:e,totalPositionInitialMargin:0,totalOpenOrderInitialMargin:0,availableBalance:e,maxWithdrawAmount:e,updateTime:Date.now()}}async setupRealtimeListeners(){if(!this.userId)throw Error("Cannot setup listeners without user ID");console.log("Setting up realtime listeners for user:",this.userId),this.cleanupListeners();try{let e=(0,o.KR)(a.Ye,".info/connected"),t=(0,o.Zy)(e,e=>{let t=e.val();console.log("Firebase connection status:",t),t?(this.state.connectionStatus="connected",this.state.error=null):(this.state.connectionStatus="disconnected",this.state.error="Connection lost",this.scheduleReconnect(this.userId,2e3)),this.notifySubscribers()}),i=(0,o.KR)(a.Ye,"users/".concat(this.userId,"/positions")),s=(0,o.Zy)(i,e=>{try{let t=e.val();console.log("Positions data received:",t?Object.keys(t).length:0,"positions"),this.state.positions=t?Object.entries(t).map(e=>{var t;let[i,s]=e,r={id:i,...s,timestamp:s.timestamp||Date.now(),entryPrice:Number(s.entryPrice)||0,markPrice:Number(s.markPrice)||Number(s.entryPrice)||0,size:Number(s.size)||0,margin:Number(s.margin)||0,leverage:Number(s.leverage)||10,pnl:Number(s.pnl)||0,pnlPercent:Number(s.pnlPercent)||0,liquidationPrice:Number(s.liquidationPrice)||0},o=null===(t=this.state.marketData[r.symbol])||void 0===t?void 0:t.price;return o&&o>0?this.updatePositionPnL(r,o):r}):[],this.state.isLoading=!1,this.updateAccountInfo(),this.notifySubscribers()}catch(e){console.error("Error processing positions data:",e),this.state.isLoading=!1,this.state.error="Failed to process positions data",this.notifySubscribers()}},e=>{console.error("Error listening to positions:",e),this.state.error="Failed to load positions",this.state.connectionStatus="error",this.state.isLoading=!1,this.notifySubscribers(),this.scheduleReconnect(this.userId,5e3)}),r=(0,o.KR)(a.Ye,"users/".concat(this.userId,"/orders")),n=(0,o.Zy)(r,e=>{try{let t=e.val();console.log("Orders data received:",t?Object.keys(t).length:0,"orders"),this.state.orders=t?Object.entries(t).map(e=>{let[t,i]=e;return{id:t,...i,timestamp:i.timestamp||Date.now()}}):[],this.updateAccountInfo(),this.notifySubscribers()}catch(e){console.error("Error processing orders data:",e)}},e=>{console.error("Error listening to orders:",e),this.state.error="Failed to load orders",this.state.connectionStatus="error",this.notifySubscribers(),this.scheduleReconnect(this.userId,5e3)}),c=(0,o.KR)(a.Ye,"users/".concat(this.userId,"/trades")),l=(0,o.Zy)(c,e=>{try{let t=e.val();console.log("Trades data received:",t?Object.keys(t).length:0,"trades"),this.state.trades=t?Object.entries(t).map(e=>{let[t,i]=e;return{id:t,...i,timestamp:i.timestamp||Date.now()}}).sort((e,t)=>t.timestamp-e.timestamp):[],this.notifySubscribers()}catch(e){console.error("Error processing trades data:",e)}},e=>{console.error("Error listening to trades:",e),this.state.error="Failed to load trade history",this.state.connectionStatus="error",this.notifySubscribers(),this.scheduleReconnect(this.userId,5e3)});this.unsubscribeFunctions=[t,()=>(0,o.AU)(i,"value",s),()=>(0,o.AU)(r,"value",n),()=>(0,o.AU)(c,"value",l)],console.log("Realtime listeners setup completed for user:",this.userId)}catch(e){throw console.error("Error setting up realtime listeners:",e),e}}cleanupListeners(){console.log("Cleaning up existing listeners"),this.unsubscribeFunctions.forEach(e=>{try{e()}catch(e){console.error("Error during listener cleanup:",e)}}),this.unsubscribeFunctions=[]}updateAccountInfo(){if(!this.state.accountInfo)return;let e=n.A.getUserBalance(),t=this.state.positions.reduce((e,t)=>e+(t.pnl||0),0),i=this.state.positions.reduce((e,t)=>e+(t.margin||0),0),s=this.state.orders.filter(e=>"NEW"===e.status).reduce((e,t)=>{var i;let s=(null===(i=this.state.marketData[t.symbol])||void 0===i?void 0:i.price)||t.price;return e+t.origQty*s/(t.leverage||10)},0),r=i+s,o=Math.max(0,e-r);this.state.accountInfo.totalWalletBalance=e,this.state.accountInfo.totalUnrealizedProfit=t,this.state.accountInfo.totalPositionInitialMargin=i,this.state.accountInfo.totalOpenOrderInitialMargin=s,this.state.accountInfo.totalMarginBalance=e+t,this.state.accountInfo.availableBalance=o,this.state.accountInfo.maxWithdrawAmount=o,this.state.accountInfo.updateTime=Date.now(),console.log("Account Info Updated:",{userBalance:e,totalMargin:i,totalOrderMargin:s,totalUsedMargin:r,availableBalance:o,positionsCount:this.state.positions.length,ordersCount:this.state.orders.filter(e=>"NEW"===e.status).length})}updateMarketData(e,t){this.state.marketData[e]={...this.state.marketData[e],...t},t.price&&(this.state.positions=this.state.positions.map(i=>i.symbol===e?this.updatePositionPnL(i,t.price):i),this.updateAccountInfo(),this.notifySubscribersDebounced())}calculatePnL(e,t){if(!e||!t||t<=0||e.entryPrice<=0||e.size<=0)return{pnl:0,pnlPercent:0};let i=("LONG"===e.side?t-e.entryPrice:e.entryPrice-t)*e.size,s=e.margin>0?i/e.margin*100:0,r=isFinite(i)?i:0,o=isFinite(s)?s:0;return{pnl:Number(r.toFixed(2)),pnlPercent:Number(o.toFixed(2))}}updatePositionPnL(e,t){let{pnl:i,pnlPercent:s}=this.calculatePnL(e,t);return{...e,markPrice:t,pnl:i,pnlPercent:s}}calculateLiquidationPrice(e,t,i){let s=.995-1/i;return"LONG"===t?e*s:e*(2-s)}async placeOrder(e){var t;if(!this.userId){let e=n.A.getFirebaseUser(),t=n.A.getUser();if(console.log("Authentication check in placeOrder:",{realtimeServiceUserId:this.userId,firebaseUser:e?{uid:e.uid,email:e.email}:null,userServiceUser:t?{id:t.id,email:t.email}:null}),e)console.log("Using Firebase user ID as fallback:",e.uid),this.userId=e.uid,await this.initializeForUser(e.uid);else if(t&&t.id)console.log("Using user service ID as fallback:",t.id),this.userId=t.id,await this.initializeForUser(t.id);else throw console.error("Authentication failed - no user ID available"),Error("User not authenticated. Please sign in and try again.")}let i=Date.now();if(i-this.lastOrderTime<this.ORDER_COOLDOWN){let e=this.ORDER_COOLDOWN-(i-this.lastOrderTime);throw Error("Please wait ".concat(Math.ceil(e/1e3)," second(s) before placing another order"))}let s=(null===(t=this.state.marketData[e.symbol])||void 0===t?void 0:t.price)||e.price||0;if(s<=0)throw Error("Invalid market price. Please try again.");let r=e.quantity*s,l=r/(e.leverage||10),u=.001*r,d=n.A.getUserBalance(),h=this.state.positions.reduce((e,t)=>e+(t.margin||0),0),m=this.state.orders.filter(e=>"NEW"===e.status).reduce((e,t)=>{var i;let s=(null===(i=this.state.marketData[t.symbol])||void 0===i?void 0:i.price)||t.price;return e+t.origQty*s/(t.leverage||10)},0),b=h+m,g=d-b,p=l+u;if(console.log("Balance Validation:",{userBalance:d,currentMargin:h,pendingOrderMargin:m,totalUsedMargin:b,availableBalance:g,requiredMargin:l,commission:u,totalRequired:p,orderValue:r,leverage:e.leverage||10}),p>g)throw Error("Insufficient balance. Required: ".concat(p.toFixed(2)," USDT, Available: ").concat(g.toFixed(2)," USDT"));if(g<10)throw Error("Insufficient balance. Minimum 10 USDT required for trading.");let y="ord_".concat(Date.now(),"_").concat(Math.random().toString(36).substr(2,9)),f={id:y,symbol:e.symbol,side:e.side,type:e.type,origQty:e.quantity,executedQty:0,price:e.price||s,status:"NEW",timestamp:Date.now(),leverage:e.leverage||10};this.lastOrderTime=i;try{if("MARKET"===e.type){console.log("Processing MARKET order - executing immediately"),await this.executeMarketOrder(f,s);let e=(0,o.KR)(a.Ye,"users/".concat(this.userId,"/executed_orders/").concat(y));await (0,o.hZ)(e,{...f,status:"FILLED",executedQty:f.origQty,executionPrice:s,createdAt:(0,o.O5)(),executedAt:(0,o.O5)()})}else{console.log("Processing LIMIT order - adding to open orders"),this.state.orders.push(f),this.updateAccountInfo(),this.notifySubscribers();let e=(0,o.KR)(a.Ye,"users/".concat(this.userId,"/orders/").concat(y));await (0,o.hZ)(e,{...f,createdAt:(0,o.O5)()})}return c.l.createTradeNotification(this.userId,"order_placed",{symbol:e.symbol,side:e.side,type:e.type,price:e.price||s,quantity:e.quantity}),y}catch(e){throw this.state.orders=this.state.orders.filter(e=>e.id!==y),this.updateAccountInfo(),this.notifySubscribers(),e}}async executeMarketOrder(e,t){var i,s;if(!this.userId)return;let r=e.origQty*t*.001,l=e.origQty*t/e.leverage,u=(null===(s=n.A.getUser())||void 0===s?void 0:null===(i=s.balance)||void 0===i?void 0:i.current)||n.A.getUserBalance(),d=this.state.positions.reduce((e,t)=>e+(t.margin||0),0),h=u-d,m=l+r;if(console.log("Market Order Execution Balance Check:",{userBalance:u,currentMargin:d,availableBalance:h,margin:l,commission:r,totalRequired:m,orderId:e.id}),m>h)throw Error("Market order execution failed: Insufficient balance. Required: ".concat(m.toFixed(2)," USDT, Available: ").concat(h.toFixed(2)," USDT"));let b="pos_".concat(Date.now(),"_").concat(Math.random().toString(36).substr(2,9)),g={id:b,symbol:e.symbol,side:"BUY"===e.side?"LONG":"SHORT",entryPrice:t,markPrice:t,size:e.origQty,margin:l,leverage:e.leverage,pnl:0,pnlPercent:0,liquidationPrice:this.calculateLiquidationPrice(t,"BUY"===e.side?"LONG":"SHORT",e.leverage),timestamp:Date.now(),orderId:e.id};this.state.positions.push(g),this.updateAccountInfo(),this.notifySubscribers(),c.l.createTradeNotification(this.userId,"position_opened",{symbol:e.symbol,side:g.side,size:e.origQty,entryPrice:t});try{let i=(0,o.KR)(a.Ye,"users/".concat(this.userId,"/positions/").concat(b));await (0,o.hZ)(i,{...g,createdAt:(0,o.O5)()});let s=n.A.getUserBalance();await n.A.updateBalance(s-r,"commission","Trading commission: ".concat(r.toFixed(2)," USDT")),console.log("Market order executed successfully:",{orderId:e.id,positionId:b,symbol:e.symbol,side:g.side,size:e.origQty,entryPrice:t})}catch(e){throw console.error("Error saving market order execution to Firebase:",e),this.state.positions=this.state.positions.filter(e=>e.id!==b),this.updateAccountInfo(),this.notifySubscribers(),e}}async executeOrder(e,t,i){var s,r;if(!this.userId)return;let l=t.origQty*i*.001,u=t.origQty*i/t.leverage,d=(null===(r=n.A.getUser())||void 0===r?void 0:null===(s=r.balance)||void 0===s?void 0:s.current)||n.A.getUserBalance(),h=this.state.positions.reduce((e,t)=>e+(t.margin||0),0),m=d-h,b=u+l;if(console.log("Limit Order Execution Balance Check:",{userBalance:d,currentMargin:h,availableBalance:m,margin:u,commission:l,totalRequired:b,orderId:e}),b>m)throw this.state.orders=this.state.orders.filter(t=>t.id!==e),this.updateAccountInfo(),this.notifySubscribers(),Error("Limit order execution failed: Insufficient balance. Required: ".concat(b.toFixed(2)," USDT, Available: ").concat(m.toFixed(2)," USDT"));let g="pos_".concat(Date.now(),"_").concat(Math.random().toString(36).substr(2,9)),p={id:g,symbol:t.symbol,side:"BUY"===t.side?"LONG":"SHORT",entryPrice:i,markPrice:i,size:t.origQty,margin:u,leverage:t.leverage,pnl:0,pnlPercent:0,liquidationPrice:this.calculateLiquidationPrice(i,"BUY"===t.side?"LONG":"SHORT",t.leverage),timestamp:Date.now(),orderId:e};this.state.positions.push(p),this.state.orders=this.state.orders.filter(t=>t.id!==e),this.updateAccountInfo(),this.notifySubscribers(),c.l.createTradeNotification(this.userId,"position_opened",{symbol:t.symbol,side:p.side,size:t.origQty,entryPrice:i});try{let s=(0,o.KR)(a.Ye,"users/".concat(this.userId,"/positions/").concat(g)),r=(0,o.KR)(a.Ye,"users/".concat(this.userId,"/orders/").concat(e));await Promise.all([(0,o.hZ)(s,{...p,createdAt:(0,o.O5)()}),(0,o.TF)(r)]);let c=n.A.getUserBalance();await n.A.updateBalance(c-l,"commission","Trading commission: ".concat(l.toFixed(2)," USDT")),console.log("Limit order executed successfully:",{orderId:e,positionId:g,symbol:t.symbol,side:p.side,size:t.origQty,entryPrice:i})}catch(e){console.error("Error saving position to Firebase:",e)}}async closePosition(e){var t;if(!this.userId)return;let i=this.state.positions.findIndex(t=>t.id===e);if(-1===i)return;let s=this.state.positions[i],r=(null===(t=this.state.marketData[s.symbol])||void 0===t?void 0:t.price)||s.markPrice,l=s.size*r*.001;this.state.positions.splice(i,1),this.updateAccountInfo(),this.notifySubscribers(),c.l.createTradeNotification(this.userId,"position_closed",{symbol:s.symbol,side:s.side,pnl:s.pnl,closePrice:r});try{let t="trade_".concat(Date.now(),"_").concat(Math.random().toString(36).substr(2,9)),i={id:t,symbol:s.symbol,side:"LONG"===s.side?"SELL":"BUY",price:r,quantity:s.size,commission:l,realizedPnl:s.pnl,timestamp:Date.now(),leverage:s.leverage,orderId:s.orderId||"",positionId:e};this.state.trades.unshift(i),this.notifySubscribers();let c=(0,o.KR)(a.Ye,"users/".concat(this.userId,"/positions/").concat(e)),u=(0,o.KR)(a.Ye,"users/".concat(this.userId,"/trades/").concat(t));await Promise.all([(0,o.TF)(c),(0,o.hZ)(u,{...i,createdAt:(0,o.O5)()})]);let d=n.A.getUserBalance();await n.A.updateBalance(d+s.pnl-l,s.pnl>0?"trade_profit":"trade_loss","Position closed: ".concat(s.pnl>0?"+":"").concat(s.pnl.toFixed(2)," USDT"))}catch(e){throw console.error("Error closing position in Firebase:",e),this.state.positions.push(s),this.updateAccountInfo(),this.notifySubscribers(),e}}async cancelOrder(e){if(!this.userId)throw Error("User not authenticated");try{await (0,o.TF)((0,o.KR)(a.Ye,"users/".concat(this.userId,"/orders/").concat(e))),this.state.orders=this.state.orders.filter(t=>t.id!==e),this.notifySubscribers(),console.log("Order canceled successfully:",e)}catch(e){throw console.error("Error canceling order:",e),e}}getState(){return{...this.state}}getMarketData(e){return this.state.marketData[e]||null}cleanup(){console.log("Cleaning up realtime trading service"),this.cleanupListeners(),this.updateTimeout&&(clearTimeout(this.updateTimeout),this.updateTimeout=null),this.reconnectTimeout&&(clearTimeout(this.reconnectTimeout),this.reconnectTimeout=null),this.userId=null,this.isInitializing=!1,this.initializationPromise=null,this.state={positions:[],orders:[],trades:[],accountInfo:null,marketData:{},isLoading:!1,error:null,connectionStatus:"disconnected"},this.notifySubscribers(),console.log("Realtime trading service cleanup completed")}async forceReconnect(){console.log("Force reconnect requested");let e=this.userId;e?(this.cleanup(),await this.initializeForUser(e)):console.log("No user to reconnect for")}getConnectionStatus(){return this.state.connectionStatus}constructor(){this.state={positions:[],orders:[],trades:[],accountInfo:null,marketData:{},isLoading:!1,error:null,connectionStatus:"disconnected"},this.subscribers=[],this.userId=null,this.unsubscribeFunctions=[],this.lastOrderTime=0,this.ORDER_COOLDOWN=1e3,this.updateTimeout=null,this.reconnectTimeout=null,this.initializationPromise=null,this.isInitializing=!1,n.A.subscribe(e=>{console.log("Realtime Trading Service - User state changed:",{hasUser:!!e,userId:null==e?void 0:e.id,currentUserId:this.userId,isInitializing:this.isInitializing,userStructure:e?Object.keys(e):null}),e&&e.id?this.userId!==e.id?(console.log("Initializing realtime trading service for user:",e.id),this.initializeForUser(e.id)):this.userId===e.id&&"disconnected"===this.state.connectionStatus&&(console.log("Reconnecting realtime trading service for existing user:",e.id),this.reconnectForUser(e.id)):(console.log("User logged out, cleaning up realtime trading service"),this.cleanup())}),this.checkForExistingUser()}}let u=new l;class d{initializeSimulators(){[{symbol:"BTCUSDT",basePrice:43200,volatility:.02},{symbol:"ETHUSDT",basePrice:2320,volatility:.025},{symbol:"SOLUSDT",basePrice:142,volatility:.03},{symbol:"ADAUSDT",basePrice:.45,volatility:.035},{symbol:"XRPUSDT",basePrice:.62,volatility:.04},{symbol:"BNBUSDT",basePrice:315,volatility:.025},{symbol:"DOGEUSDT",basePrice:.08,volatility:.05},{symbol:"TRXUSDT",basePrice:.12,volatility:.04},{symbol:"LINKUSDT",basePrice:14.5,volatility:.03},{symbol:"AVAXUSDT",basePrice:38.2,volatility:.035}].forEach(e=>{this.simulators.set(e.symbol,{symbol:e.symbol,basePrice:e.basePrice,volatility:e.volatility,trend:(Math.random()-.5)*.001,lastPrice:e.basePrice,lastUpdate:Date.now()})})}start(){this.intervalId||(this.intervalId=setInterval(()=>{this.updatePrices()},2e3))}stop(){this.intervalId&&(clearInterval(this.intervalId),this.intervalId=null)}updatePrices(){this.simulators.forEach(e=>{let t=Date.now(),i=(t-e.lastUpdate)/1e3,s=(Math.random()-.5)*e.volatility*i,r=e.trend*i,o=e.lastPrice*(1+(s+r)),a=o-e.lastPrice,n=a/e.lastPrice*100;.01>Math.random()&&(e.trend=(Math.random()-.5)*.001),e.lastPrice=o,e.lastUpdate=t;let c={symbol:e.symbol,price:o,priceChange:a,priceChangePercent:n,volume:1e6*Math.random(),timestamp:t};this.notifySubscribers(e.symbol,c)})}subscribe(e){return this.subscribers.push(e),()=>{let t=this.subscribers.indexOf(e);t>-1&&this.subscribers.splice(t,1)}}notifySubscribers(e,t){this.subscribers.forEach(i=>i(e,t))}getCurrentPrice(e){let t=this.simulators.get(e);return t?t.lastPrice:null}addSymbol(e,t){let i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:.03;this.simulators.has(e)||this.simulators.set(e,{symbol:e,basePrice:t,volatility:i,trend:(Math.random()-.5)*.001,lastPrice:t,lastUpdate:Date.now()})}removeSymbol(e){this.simulators.delete(e)}constructor(){this.simulators=new Map,this.intervalId=null,this.subscribers=[],this.initializeSimulators()}}let h=new d,m=(0,r.createContext)(void 0);function b(e){let{children:t}=e,[i,o]=(0,r.useState)({positions:[],orders:[],trades:[],marketData:{},accountInfo:null,isLoading:!1,error:null});(0,r.useEffect)(()=>{let e=u.subscribe(e=>{console.log("Trading context received state update:",{positions:e.positions.length,orders:e.orders.length,trades:e.trades.length,isLoading:e.isLoading,error:e.error}),o(e)}),t=u.getState();return t&&(console.log("Trading context initial state sync:",{positions:t.positions.length,orders:t.orders.length,trades:t.trades.length}),o(t)),e},[]),(0,r.useEffect)(()=>{let e=h.subscribe((e,t)=>{u.updateMarketData(e,t)});return h.start(),()=>{e(),h.stop()}},[]);let a=(0,r.useCallback)(async e=>{try{return await u.placeOrder(e)}catch(e){throw console.error("Failed to place order:",e),e}},[]),n=(0,r.useCallback)(async e=>{try{return console.log("Cancel order not implemented yet:",e),!0}catch(e){throw console.error("Failed to cancel order:",e),e}},[]),c=(0,r.useCallback)(async e=>{try{return await u.closePosition(e),!0}catch(e){throw console.error("Failed to close position:",e),e}},[]),l=(0,r.useCallback)(async e=>{try{return console.log("Position update not implemented in Firebase service yet:",e),!0}catch(e){throw console.error("Failed to update position:",e),e}},[]),d=(0,r.useCallback)((e,t)=>{u.updateMarketData(e,t)},[]),b=(0,r.useCallback)(e=>{console.log("Account info update not needed with Realtime service:",e)},[]),g=(0,r.useCallback)(()=>{o(e=>({...e,error:null}))},[]),p=(0,r.useCallback)(e=>u.getMarketData(e),[]),y=(0,r.useCallback)(e=>i.positions.find(t=>t.symbol===e)||null,[i.positions]),f=(0,r.useCallback)(()=>i.accountInfo,[i.accountInfo]),I=(0,r.useCallback)(()=>i.positions.reduce((e,t)=>e+t.pnl,0),[i.positions]),v=(0,r.useCallback)(()=>i.positions.reduce((e,t)=>e+t.margin,0),[i.positions]),P=(0,r.useCallback)(()=>{var e;return(null===(e=i.accountInfo)||void 0===e?void 0:e.availableBalance)||0},[i.accountInfo]),S={positions:i.positions,orders:i.orders,trades:i.trades,marketData:i.marketData,accountInfo:i.accountInfo,isLoading:i.isLoading,error:i.error,state:i,placeOrder:a,cancelOrder:n,closePosition:c,updatePosition:l,updateMarketData:d,updateAccountInfo:b,clearError:g,getMarketData:p,getPositionBySymbol:y,getAccountInfo:f,getTotalPnL:I,getTotalMargin:v,getAvailableBalance:P};return(0,s.jsx)(m.Provider,{value:S,children:t})}function g(){let e=(0,r.useContext)(m);if(void 0===e)throw Error("useTrading must be used within a TradingProvider");return e}}}]);