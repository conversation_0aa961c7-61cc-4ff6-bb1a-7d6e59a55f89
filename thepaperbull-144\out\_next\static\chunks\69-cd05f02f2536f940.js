"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[69],{17069:(t,e,i)=>{i.d(e,{rM:()=>b,fx:()=>p});var s=i(95155),r=i(12115),o=i(81115),a=i(98915),n=i(50475),c=i(27759);class l{async checkForExistingUser(){let t=0,e=async()=>{t++;let e=n.A.getUser(),i=n.A.getFirebaseUser();return(console.log("User check attempt ".concat(t,":"),{currentUser:e?{id:e.id,email:e.email}:null,firebaseUser:i?{uid:i.uid,email:i.email}:null,hasUserId:!!this.userId}),e&&e.id&&!this.userId)?(console.log("Found existing user on startup, initializing:",e.id),await this.initializeForUser(e.id),!0):!i||this.userId||e?!!this.userId:(console.log("Firebase user exists, waiting for user service to load..."),!1)};if(!await e()){for(let t=1;t<5;t++)if(await new Promise(e=>setTimeout(e,500*t)),await e())return;console.log("No user found after all attempts, service will initialize when user logs in")}}subscribe(t){return this.subscribers.push(t),t(this.state),()=>{let e=this.subscribers.indexOf(t);e>-1&&this.subscribers.splice(e,1)}}notifySubscribers(){this.subscribers.forEach(t=>t(this.state))}notifySubscribersDebounced(){this.updateTimeout&&clearTimeout(this.updateTimeout),this.updateTimeout=setTimeout(()=>{this.notifySubscribers()},100)}async initializeForUser(t){if(this.isInitializing&&this.initializationPromise)return console.log("Already initializing, waiting for completion..."),this.initializationPromise;this.isInitializing=!0,this.initializationPromise=this._doInitializeForUser(t);try{await this.initializationPromise}finally{this.isInitializing=!1,this.initializationPromise=null}}async _doInitializeForUser(t){console.log("Starting initialization for user:",t),this.userId&&this.userId!==t&&(console.log("Cleaning up previous user connections"),this.cleanup()),this.userId=t,this.state.isLoading=!0,this.state.connectionStatus="connecting",this.state.error=null,this.notifySubscribers();try{await this.initializeAccountInfo(),await this.setupRealtimeListeners(),this.state.isLoading=!1,this.state.connectionStatus="connected",this.state.error=null,this.notifySubscribers(),console.log("Realtime trading service initialized successfully for user:",t)}catch(e){throw console.error("Error initializing realtime trading service:",e),this.state.isLoading=!1,this.state.connectionStatus="error",this.state.error="Failed to initialize trading service",this.notifySubscribers(),this.scheduleReconnect(t),e}}async reconnectForUser(t){console.log("Reconnecting for user:",t),this.reconnectTimeout&&(clearTimeout(this.reconnectTimeout),this.reconnectTimeout=null);try{await this.initializeForUser(t)}catch(t){console.error("Reconnection failed:",t)}}scheduleReconnect(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:5e3;this.reconnectTimeout&&clearTimeout(this.reconnectTimeout),console.log("Scheduling reconnect in ".concat(e,"ms for user:"),t),this.reconnectTimeout=setTimeout(()=>{this.userId===t&&(console.log("Attempting scheduled reconnect for user:",t),this.reconnectForUser(t))},e)}async initializeAccountInfo(){if(!this.userId)return;let t=n.A.getUserBalance();this.state.accountInfo={totalWalletBalance:t,totalUnrealizedProfit:0,totalMarginBalance:t,totalPositionInitialMargin:0,totalOpenOrderInitialMargin:0,availableBalance:t,maxWithdrawAmount:t,updateTime:Date.now()}}async setupRealtimeListeners(){if(!this.userId)throw Error("Cannot setup listeners without user ID");console.log("Setting up realtime listeners for user:",this.userId),this.cleanupListeners();try{let t=(0,o.KR)(a.Ye,".info/connected"),e=(0,o.Zy)(t,t=>{let e=t.val();console.log("Firebase connection status:",e),e?(this.state.connectionStatus="connected",this.state.error=null):(this.state.connectionStatus="disconnected",this.state.error="Connection lost",this.scheduleReconnect(this.userId,2e3)),this.notifySubscribers()}),i=(0,o.KR)(a.Ye,"users/".concat(this.userId,"/positions")),s=(0,o.Zy)(i,t=>{try{let e=t.val();console.log("Positions data received:",e?Object.keys(e).length:0,"positions"),this.state.positions=e?Object.entries(e).map(t=>{let[e,i]=t;return{id:e,...i,timestamp:i.timestamp||Date.now()}}):[],this.updateAccountInfo(),this.notifySubscribers()}catch(t){console.error("Error processing positions data:",t)}},t=>{console.error("Error listening to positions:",t),this.state.error="Failed to load positions",this.state.connectionStatus="error",this.notifySubscribers(),this.scheduleReconnect(this.userId,5e3)}),r=(0,o.KR)(a.Ye,"users/".concat(this.userId,"/orders")),n=(0,o.Zy)(r,t=>{try{let e=t.val();console.log("Orders data received:",e?Object.keys(e).length:0,"orders"),this.state.orders=e?Object.entries(e).map(t=>{let[e,i]=t;return{id:e,...i,timestamp:i.timestamp||Date.now()}}):[],this.updateAccountInfo(),this.notifySubscribers()}catch(t){console.error("Error processing orders data:",t)}},t=>{console.error("Error listening to orders:",t),this.state.error="Failed to load orders",this.state.connectionStatus="error",this.notifySubscribers(),this.scheduleReconnect(this.userId,5e3)}),c=(0,o.KR)(a.Ye,"users/".concat(this.userId,"/trades")),l=(0,o.Zy)(c,t=>{try{let e=t.val();console.log("Trades data received:",e?Object.keys(e).length:0,"trades"),this.state.trades=e?Object.entries(e).map(t=>{let[e,i]=t;return{id:e,...i,timestamp:i.timestamp||Date.now()}}).sort((t,e)=>e.timestamp-t.timestamp):[],this.notifySubscribers()}catch(t){console.error("Error processing trades data:",t)}},t=>{console.error("Error listening to trades:",t),this.state.error="Failed to load trade history",this.state.connectionStatus="error",this.notifySubscribers(),this.scheduleReconnect(this.userId,5e3)});this.unsubscribeFunctions=[e,()=>(0,o.AU)(i,"value",s),()=>(0,o.AU)(r,"value",n),()=>(0,o.AU)(c,"value",l)],console.log("Realtime listeners setup completed for user:",this.userId)}catch(t){throw console.error("Error setting up realtime listeners:",t),t}}cleanupListeners(){console.log("Cleaning up existing listeners"),this.unsubscribeFunctions.forEach(t=>{try{t()}catch(t){console.error("Error during listener cleanup:",t)}}),this.unsubscribeFunctions=[]}updateAccountInfo(){if(!this.state.accountInfo)return;let t=n.A.getUserBalance(),e=this.state.positions.reduce((t,e)=>t+(e.pnl||0),0),i=this.state.positions.reduce((t,e)=>t+(e.margin||0),0),s=this.state.orders.filter(t=>"NEW"===t.status).reduce((t,e)=>{var i;let s=(null===(i=this.state.marketData[e.symbol])||void 0===i?void 0:i.price)||e.price;return t+e.origQty*s/(e.leverage||10)},0),r=i+s,o=Math.max(0,t-r);this.state.accountInfo.totalWalletBalance=t,this.state.accountInfo.totalUnrealizedProfit=e,this.state.accountInfo.totalPositionInitialMargin=i,this.state.accountInfo.totalOpenOrderInitialMargin=s,this.state.accountInfo.totalMarginBalance=t+e,this.state.accountInfo.availableBalance=o,this.state.accountInfo.maxWithdrawAmount=o,this.state.accountInfo.updateTime=Date.now(),console.log("Account Info Updated:",{userBalance:t,totalMargin:i,totalOrderMargin:s,totalUsedMargin:r,availableBalance:o,positionsCount:this.state.positions.length,ordersCount:this.state.orders.filter(t=>"NEW"===t.status).length})}updateMarketData(t,e){this.state.marketData[t]={...this.state.marketData[t],...e},e.price&&(this.state.positions=this.state.positions.map(i=>i.symbol===t?this.updatePositionPnL(i,e.price):i),this.updateAccountInfo(),this.notifySubscribersDebounced())}calculatePnL(t,e){let i=("LONG"===t.side?e-t.entryPrice:t.entryPrice-e)*t.size,s=t.margin>0?i/t.margin*100:0;return{pnl:Number(i.toFixed(2)),pnlPercent:Number(s.toFixed(2))}}updatePositionPnL(t,e){let{pnl:i,pnlPercent:s}=this.calculatePnL(t,e);return{...t,markPrice:e,pnl:i,pnlPercent:s}}calculateLiquidationPrice(t,e,i){let s=.995-1/i;return"LONG"===e?t*s:t*(2-s)}async placeOrder(t){var e;if(!this.userId){let t=n.A.getFirebaseUser(),e=n.A.getUser();if(console.log("Authentication check in placeOrder:",{realtimeServiceUserId:this.userId,firebaseUser:t?{uid:t.uid,email:t.email}:null,userServiceUser:e?{id:e.id,email:e.email}:null}),t)console.log("Using Firebase user ID as fallback:",t.uid),this.userId=t.uid,await this.initializeForUser(t.uid);else if(e&&e.id)console.log("Using user service ID as fallback:",e.id),this.userId=e.id,await this.initializeForUser(e.id);else throw console.error("Authentication failed - no user ID available"),Error("User not authenticated. Please sign in and try again.")}let i=Date.now();if(i-this.lastOrderTime<this.ORDER_COOLDOWN){let t=this.ORDER_COOLDOWN-(i-this.lastOrderTime);throw Error("Please wait ".concat(Math.ceil(t/1e3)," second(s) before placing another order"))}let s=(null===(e=this.state.marketData[t.symbol])||void 0===e?void 0:e.price)||t.price||0;if(s<=0)throw Error("Invalid market price. Please try again.");let r=t.quantity*s,l=r/(t.leverage||10),u=.001*r,d=n.A.getUserBalance(),h=this.state.positions.reduce((t,e)=>t+(e.margin||0),0),m=this.state.orders.filter(t=>"NEW"===t.status).reduce((t,e)=>{var i;let s=(null===(i=this.state.marketData[e.symbol])||void 0===i?void 0:i.price)||e.price;return t+e.origQty*s/(e.leverage||10)},0),b=h+m,p=d-b,g=l+u;if(console.log("Balance Validation:",{userBalance:d,currentMargin:h,pendingOrderMargin:m,totalUsedMargin:b,availableBalance:p,requiredMargin:l,commission:u,totalRequired:g,orderValue:r,leverage:t.leverage||10}),g>p)throw Error("Insufficient balance. Required: ".concat(g.toFixed(2)," USDT, Available: ").concat(p.toFixed(2)," USDT"));if(p<10)throw Error("Insufficient balance. Minimum 10 USDT required for trading.");let y="ord_".concat(Date.now(),"_").concat(Math.random().toString(36).substr(2,9)),f={id:y,symbol:t.symbol,side:t.side,type:t.type,origQty:t.quantity,executedQty:0,price:t.price||s,status:"NEW",timestamp:Date.now(),leverage:t.leverage||10};this.state.orders.push(f),this.updateAccountInfo(),this.notifySubscribers(),this.lastOrderTime=i;try{let e=(0,o.KR)(a.Ye,"users/".concat(this.userId,"/orders/").concat(y));return await (0,o.hZ)(e,{...f,createdAt:(0,o.O5)()}),"MARKET"===t.type&&await this.executeOrder(y,f,s),c.l.createTradeNotification(this.userId,"order_placed",{symbol:t.symbol,side:t.side,type:t.type,price:t.price||s,quantity:t.quantity}),y}catch(t){throw this.state.orders=this.state.orders.filter(t=>t.id!==y),this.updateAccountInfo(),this.notifySubscribers(),t}}async executeOrder(t,e,i){var s,r;if(!this.userId)return;let l=e.origQty*i*.001,u=e.origQty*i/e.leverage,d=(null===(r=n.A.getUser())||void 0===r?void 0:null===(s=r.balance)||void 0===s?void 0:s.current)||n.A.getUserBalance(),h=this.state.positions.reduce((t,e)=>t+(e.margin||0),0),m=d-h,b=u+l;if(console.log("Execution Balance Check:",{userBalance:d,currentMargin:h,availableBalance:m,margin:u,commission:l,totalRequired:b,orderId:t}),b>m)throw this.state.orders=this.state.orders.filter(e=>e.id!==t),this.updateAccountInfo(),this.notifySubscribers(),Error("Execution failed: Insufficient balance. Required: ".concat(b.toFixed(2)," USDT, Available: ").concat(m.toFixed(2)," USDT"));let p="pos_".concat(Date.now(),"_").concat(Math.random().toString(36).substr(2,9)),g={id:p,symbol:e.symbol,side:"BUY"===e.side?"LONG":"SHORT",entryPrice:i,markPrice:i,size:e.origQty,margin:u,leverage:e.leverage,pnl:0,pnlPercent:0,liquidationPrice:this.calculateLiquidationPrice(i,"BUY"===e.side?"LONG":"SHORT",e.leverage),timestamp:Date.now(),orderId:t};this.state.positions.push(g);let y=this.state.orders.findIndex(e=>e.id===t);-1!==y&&(this.state.orders[y].status="FILLED",this.state.orders[y].executedQty=e.origQty),this.updateAccountInfo(),this.notifySubscribers(),c.l.createTradeNotification(this.userId,"position_opened",{symbol:e.symbol,side:g.side,size:e.origQty,entryPrice:i});try{let e=(0,o.KR)(a.Ye,"users/".concat(this.userId,"/positions/").concat(p)),i=(0,o.KR)(a.Ye,"users/".concat(this.userId,"/orders/").concat(t));await Promise.all([(0,o.hZ)(e,{...g,createdAt:(0,o.O5)()}),(0,o.hZ)(i,{...this.state.orders[y],updatedAt:(0,o.O5)()})]);let s=n.A.getUserBalance();await n.A.updateBalance(s-l,"commission","Trading commission: ".concat(l.toFixed(2)," USDT"))}catch(t){console.error("Error saving position to Firebase:",t)}}async closePosition(t){var e;if(!this.userId)return;let i=this.state.positions.findIndex(e=>e.id===t);if(-1===i)return;let s=this.state.positions[i],r=(null===(e=this.state.marketData[s.symbol])||void 0===e?void 0:e.price)||s.markPrice,l=s.size*r*.001;this.state.positions.splice(i,1),this.updateAccountInfo(),this.notifySubscribers(),c.l.createTradeNotification(this.userId,"position_closed",{symbol:s.symbol,side:s.side,pnl:s.pnl,closePrice:r});try{let e="trade_".concat(Date.now(),"_").concat(Math.random().toString(36).substr(2,9)),i={id:e,symbol:s.symbol,side:"LONG"===s.side?"SELL":"BUY",price:r,quantity:s.size,commission:l,realizedPnl:s.pnl,timestamp:Date.now(),leverage:s.leverage,orderId:s.orderId||"",positionId:t};this.state.trades.unshift(i),this.notifySubscribers();let c=(0,o.KR)(a.Ye,"users/".concat(this.userId,"/positions/").concat(t)),u=(0,o.KR)(a.Ye,"users/".concat(this.userId,"/trades/").concat(e));await Promise.all([(0,o.TF)(c),(0,o.hZ)(u,{...i,createdAt:(0,o.O5)()})]);let d=n.A.getUserBalance();await n.A.updateBalance(d+s.pnl-l,s.pnl>0?"trade_profit":"trade_loss","Position closed: ".concat(s.pnl>0?"+":"").concat(s.pnl.toFixed(2)," USDT"))}catch(t){throw console.error("Error closing position in Firebase:",t),this.state.positions.push(s),this.updateAccountInfo(),this.notifySubscribers(),t}}async cancelOrder(t){if(!this.userId)throw Error("User not authenticated");try{await (0,o.TF)((0,o.KR)(a.Ye,"users/".concat(this.userId,"/orders/").concat(t))),this.state.orders=this.state.orders.filter(e=>e.id!==t),this.notifySubscribers(),console.log("Order canceled successfully:",t)}catch(t){throw console.error("Error canceling order:",t),t}}getState(){return{...this.state}}getMarketData(t){return this.state.marketData[t]||null}cleanup(){console.log("Cleaning up realtime trading service"),this.cleanupListeners(),this.updateTimeout&&(clearTimeout(this.updateTimeout),this.updateTimeout=null),this.reconnectTimeout&&(clearTimeout(this.reconnectTimeout),this.reconnectTimeout=null),this.userId=null,this.isInitializing=!1,this.initializationPromise=null,this.state={positions:[],orders:[],trades:[],accountInfo:null,marketData:{},isLoading:!1,error:null,connectionStatus:"disconnected"},this.notifySubscribers(),console.log("Realtime trading service cleanup completed")}async forceReconnect(){console.log("Force reconnect requested");let t=this.userId;t?(this.cleanup(),await this.initializeForUser(t)):console.log("No user to reconnect for")}getConnectionStatus(){return this.state.connectionStatus}constructor(){this.state={positions:[],orders:[],trades:[],accountInfo:null,marketData:{},isLoading:!1,error:null,connectionStatus:"disconnected"},this.subscribers=[],this.userId=null,this.unsubscribeFunctions=[],this.lastOrderTime=0,this.ORDER_COOLDOWN=1e3,this.updateTimeout=null,this.reconnectTimeout=null,this.initializationPromise=null,this.isInitializing=!1,n.A.subscribe(t=>{console.log("Realtime Trading Service - User state changed:",{hasUser:!!t,userId:null==t?void 0:t.id,currentUserId:this.userId,isInitializing:this.isInitializing,userStructure:t?Object.keys(t):null}),t&&t.id?this.userId!==t.id?(console.log("Initializing realtime trading service for user:",t.id),this.initializeForUser(t.id)):this.userId===t.id&&"disconnected"===this.state.connectionStatus&&(console.log("Reconnecting realtime trading service for existing user:",t.id),this.reconnectForUser(t.id)):(console.log("User logged out, cleaning up realtime trading service"),this.cleanup())}),this.checkForExistingUser()}}let u=new l;class d{initializeSimulators(){[{symbol:"BTCUSDT",basePrice:43200,volatility:.02},{symbol:"ETHUSDT",basePrice:2320,volatility:.025},{symbol:"SOLUSDT",basePrice:142,volatility:.03},{symbol:"ADAUSDT",basePrice:.45,volatility:.035},{symbol:"XRPUSDT",basePrice:.62,volatility:.04},{symbol:"BNBUSDT",basePrice:315,volatility:.025},{symbol:"DOGEUSDT",basePrice:.08,volatility:.05},{symbol:"TRXUSDT",basePrice:.12,volatility:.04},{symbol:"LINKUSDT",basePrice:14.5,volatility:.03},{symbol:"AVAXUSDT",basePrice:38.2,volatility:.035}].forEach(t=>{this.simulators.set(t.symbol,{symbol:t.symbol,basePrice:t.basePrice,volatility:t.volatility,trend:(Math.random()-.5)*.001,lastPrice:t.basePrice,lastUpdate:Date.now()})})}start(){this.intervalId||(this.intervalId=setInterval(()=>{this.updatePrices()},2e3))}stop(){this.intervalId&&(clearInterval(this.intervalId),this.intervalId=null)}updatePrices(){this.simulators.forEach(t=>{let e=Date.now(),i=(e-t.lastUpdate)/1e3,s=(Math.random()-.5)*t.volatility*i,r=t.trend*i,o=t.lastPrice*(1+(s+r)),a=o-t.lastPrice,n=a/t.lastPrice*100;.01>Math.random()&&(t.trend=(Math.random()-.5)*.001),t.lastPrice=o,t.lastUpdate=e;let c={symbol:t.symbol,price:o,priceChange:a,priceChangePercent:n,volume:1e6*Math.random(),timestamp:e};this.notifySubscribers(t.symbol,c)})}subscribe(t){return this.subscribers.push(t),()=>{let e=this.subscribers.indexOf(t);e>-1&&this.subscribers.splice(e,1)}}notifySubscribers(t,e){this.subscribers.forEach(i=>i(t,e))}getCurrentPrice(t){let e=this.simulators.get(t);return e?e.lastPrice:null}addSymbol(t,e){let i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:.03;this.simulators.has(t)||this.simulators.set(t,{symbol:t,basePrice:e,volatility:i,trend:(Math.random()-.5)*.001,lastPrice:e,lastUpdate:Date.now()})}removeSymbol(t){this.simulators.delete(t)}constructor(){this.simulators=new Map,this.intervalId=null,this.subscribers=[],this.initializeSimulators()}}let h=new d,m=(0,r.createContext)(void 0);function b(t){let{children:e}=t,[i,o]=(0,r.useState)({positions:[],orders:[],trades:[],marketData:{},accountInfo:null,isLoading:!1,error:null});(0,r.useEffect)(()=>{let t=u.subscribe(t=>{console.log("Trading context received state update:",{positions:t.positions.length,orders:t.orders.length,trades:t.trades.length,isLoading:t.isLoading,error:t.error}),o(t)}),e=u.getState();return e&&(console.log("Trading context initial state sync:",{positions:e.positions.length,orders:e.orders.length,trades:e.trades.length}),o(e)),t},[]),(0,r.useEffect)(()=>{let t=h.subscribe((t,e)=>{u.updateMarketData(t,e)});return h.start(),()=>{t(),h.stop()}},[]);let a=(0,r.useCallback)(async t=>{try{return await u.placeOrder(t)}catch(t){throw console.error("Failed to place order:",t),t}},[]),n=(0,r.useCallback)(async t=>{try{return console.log("Cancel order not implemented yet:",t),!0}catch(t){throw console.error("Failed to cancel order:",t),t}},[]),c=(0,r.useCallback)(async t=>{try{return await u.closePosition(t),!0}catch(t){throw console.error("Failed to close position:",t),t}},[]),l=(0,r.useCallback)(async t=>{try{return console.log("Position update not implemented in Firebase service yet:",t),!0}catch(t){throw console.error("Failed to update position:",t),t}},[]),d=(0,r.useCallback)((t,e)=>{u.updateMarketData(t,e)},[]),b=(0,r.useCallback)(t=>{console.log("Account info update not needed with Realtime service:",t)},[]),p=(0,r.useCallback)(()=>{o(t=>({...t,error:null}))},[]),g=(0,r.useCallback)(t=>u.getMarketData(t),[]),y=(0,r.useCallback)(t=>i.positions.find(e=>e.symbol===t)||null,[i.positions]),f=(0,r.useCallback)(()=>i.accountInfo,[i.accountInfo]),I=(0,r.useCallback)(()=>i.positions.reduce((t,e)=>t+e.pnl,0),[i.positions]),v=(0,r.useCallback)(()=>i.positions.reduce((t,e)=>t+e.margin,0),[i.positions]),S=(0,r.useCallback)(()=>{var t;return(null===(t=i.accountInfo)||void 0===t?void 0:t.availableBalance)||0},[i.accountInfo]),U={positions:i.positions,orders:i.orders,trades:i.trades,marketData:i.marketData,accountInfo:i.accountInfo,isLoading:i.isLoading,error:i.error,state:i,placeOrder:a,cancelOrder:n,closePosition:c,updatePosition:l,updateMarketData:d,updateAccountInfo:b,clearError:p,getMarketData:g,getPositionBySymbol:y,getAccountInfo:f,getTotalPnL:I,getTotalMargin:v,getAvailableBalance:S};return(0,s.jsx)(m.Provider,{value:U,children:e})}function p(){let t=(0,r.useContext)(m);if(void 0===t)throw Error("useTrading must be used within a TradingProvider");return t}}}]);