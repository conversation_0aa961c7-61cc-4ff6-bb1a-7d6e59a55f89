(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[585],{2107:(t,e,r)=>{"use strict";r.d(e,{VV:()=>i,jz:()=>n});var n,i,o="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{},s={};(function(){function t(){this.blockSize=-1,this.blockSize=64,this.g=[,,,,],this.B=Array(this.blockSize),this.o=this.h=0,this.s()}function e(t,e,r){r||(r=0);var n=Array(16);if("string"==typeof e)for(var i=0;16>i;++i)n[i]=e.charCodeAt(r++)|e.charCodeAt(r++)<<8|e.charCodeAt(r++)<<16|e.charCodeAt(r++)<<24;else for(i=0;16>i;++i)n[i]=e[r++]|e[r++]<<8|e[r++]<<16|e[r++]<<24;e=t.g[0],r=t.g[1],i=t.g[2];var o=t.g[3],s=e+(o^r&(i^o))+n[0]+0xd76aa478&0xffffffff;s=o+(i^(e=r+(s<<7&0xffffffff|s>>>25))&(r^i))+n[1]+0xe8c7b756&0xffffffff,s=i+(r^(o=e+(s<<12&0xffffffff|s>>>20))&(e^r))+n[2]+0x242070db&0xffffffff,s=r+(e^(i=o+(s<<17&0xffffffff|s>>>15))&(o^e))+n[3]+0xc1bdceee&0xffffffff,s=e+(o^(r=i+(s<<22&0xffffffff|s>>>10))&(i^o))+n[4]+0xf57c0faf&0xffffffff,s=o+(i^(e=r+(s<<7&0xffffffff|s>>>25))&(r^i))+n[5]+0x4787c62a&0xffffffff,s=i+(r^(o=e+(s<<12&0xffffffff|s>>>20))&(e^r))+n[6]+0xa8304613&0xffffffff,s=r+(e^(i=o+(s<<17&0xffffffff|s>>>15))&(o^e))+n[7]+0xfd469501&0xffffffff,s=e+(o^(r=i+(s<<22&0xffffffff|s>>>10))&(i^o))+n[8]+0x698098d8&0xffffffff,s=o+(i^(e=r+(s<<7&0xffffffff|s>>>25))&(r^i))+n[9]+0x8b44f7af&0xffffffff,s=i+(r^(o=e+(s<<12&0xffffffff|s>>>20))&(e^r))+n[10]+0xffff5bb1&0xffffffff,s=r+(e^(i=o+(s<<17&0xffffffff|s>>>15))&(o^e))+n[11]+0x895cd7be&0xffffffff,s=e+(o^(r=i+(s<<22&0xffffffff|s>>>10))&(i^o))+n[12]+0x6b901122&0xffffffff,s=o+(i^(e=r+(s<<7&0xffffffff|s>>>25))&(r^i))+n[13]+0xfd987193&0xffffffff,s=i+(r^(o=e+(s<<12&0xffffffff|s>>>20))&(e^r))+n[14]+0xa679438e&0xffffffff,s=r+(e^(i=o+(s<<17&0xffffffff|s>>>15))&(o^e))+n[15]+0x49b40821&0xffffffff,r=i+(s<<22&0xffffffff|s>>>10),s=e+(i^o&(r^i))+n[1]+0xf61e2562&0xffffffff,e=r+(s<<5&0xffffffff|s>>>27),s=o+(r^i&(e^r))+n[6]+0xc040b340&0xffffffff,o=e+(s<<9&0xffffffff|s>>>23),s=i+(e^r&(o^e))+n[11]+0x265e5a51&0xffffffff,i=o+(s<<14&0xffffffff|s>>>18),s=r+(o^e&(i^o))+n[0]+0xe9b6c7aa&0xffffffff,r=i+(s<<20&0xffffffff|s>>>12),s=e+(i^o&(r^i))+n[5]+0xd62f105d&0xffffffff,e=r+(s<<5&0xffffffff|s>>>27),s=o+(r^i&(e^r))+n[10]+0x2441453&0xffffffff,o=e+(s<<9&0xffffffff|s>>>23),s=i+(e^r&(o^e))+n[15]+0xd8a1e681&0xffffffff,i=o+(s<<14&0xffffffff|s>>>18),s=r+(o^e&(i^o))+n[4]+0xe7d3fbc8&0xffffffff,r=i+(s<<20&0xffffffff|s>>>12),s=e+(i^o&(r^i))+n[9]+0x21e1cde6&0xffffffff,e=r+(s<<5&0xffffffff|s>>>27),s=o+(r^i&(e^r))+n[14]+0xc33707d6&0xffffffff,o=e+(s<<9&0xffffffff|s>>>23),s=i+(e^r&(o^e))+n[3]+0xf4d50d87&0xffffffff,i=o+(s<<14&0xffffffff|s>>>18),s=r+(o^e&(i^o))+n[8]+0x455a14ed&0xffffffff,r=i+(s<<20&0xffffffff|s>>>12),s=e+(i^o&(r^i))+n[13]+0xa9e3e905&0xffffffff,e=r+(s<<5&0xffffffff|s>>>27),s=o+(r^i&(e^r))+n[2]+0xfcefa3f8&0xffffffff,o=e+(s<<9&0xffffffff|s>>>23),s=i+(e^r&(o^e))+n[7]+0x676f02d9&0xffffffff,i=o+(s<<14&0xffffffff|s>>>18),s=r+(o^e&(i^o))+n[12]+0x8d2a4c8a&0xffffffff,s=e+((r=i+(s<<20&0xffffffff|s>>>12))^i^o)+n[5]+0xfffa3942&0xffffffff,s=o+((e=r+(s<<4&0xffffffff|s>>>28))^r^i)+n[8]+0x8771f681&0xffffffff,s=i+((o=e+(s<<11&0xffffffff|s>>>21))^e^r)+n[11]+0x6d9d6122&0xffffffff,s=r+((i=o+(s<<16&0xffffffff|s>>>16))^o^e)+n[14]+0xfde5380c&0xffffffff,s=e+((r=i+(s<<23&0xffffffff|s>>>9))^i^o)+n[1]+0xa4beea44&0xffffffff,s=o+((e=r+(s<<4&0xffffffff|s>>>28))^r^i)+n[4]+0x4bdecfa9&0xffffffff,s=i+((o=e+(s<<11&0xffffffff|s>>>21))^e^r)+n[7]+0xf6bb4b60&0xffffffff,s=r+((i=o+(s<<16&0xffffffff|s>>>16))^o^e)+n[10]+0xbebfbc70&0xffffffff,s=e+((r=i+(s<<23&0xffffffff|s>>>9))^i^o)+n[13]+0x289b7ec6&0xffffffff,s=o+((e=r+(s<<4&0xffffffff|s>>>28))^r^i)+n[0]+0xeaa127fa&0xffffffff,s=i+((o=e+(s<<11&0xffffffff|s>>>21))^e^r)+n[3]+0xd4ef3085&0xffffffff,s=r+((i=o+(s<<16&0xffffffff|s>>>16))^o^e)+n[6]+0x4881d05&0xffffffff,s=e+((r=i+(s<<23&0xffffffff|s>>>9))^i^o)+n[9]+0xd9d4d039&0xffffffff,s=o+((e=r+(s<<4&0xffffffff|s>>>28))^r^i)+n[12]+0xe6db99e5&0xffffffff,s=i+((o=e+(s<<11&0xffffffff|s>>>21))^e^r)+n[15]+0x1fa27cf8&0xffffffff,s=r+((i=o+(s<<16&0xffffffff|s>>>16))^o^e)+n[2]+0xc4ac5665&0xffffffff,r=i+(s<<23&0xffffffff|s>>>9),s=e+(i^(r|~o))+n[0]+0xf4292244&0xffffffff,e=r+(s<<6&0xffffffff|s>>>26),s=o+(r^(e|~i))+n[7]+0x432aff97&0xffffffff,o=e+(s<<10&0xffffffff|s>>>22),s=i+(e^(o|~r))+n[14]+0xab9423a7&0xffffffff,i=o+(s<<15&0xffffffff|s>>>17),s=r+(o^(i|~e))+n[5]+0xfc93a039&0xffffffff,r=i+(s<<21&0xffffffff|s>>>11),s=e+(i^(r|~o))+n[12]+0x655b59c3&0xffffffff,e=r+(s<<6&0xffffffff|s>>>26),s=o+(r^(e|~i))+n[3]+0x8f0ccc92&0xffffffff,o=e+(s<<10&0xffffffff|s>>>22),s=i+(e^(o|~r))+n[10]+0xffeff47d&0xffffffff,i=o+(s<<15&0xffffffff|s>>>17),s=r+(o^(i|~e))+n[1]+0x85845dd1&0xffffffff,r=i+(s<<21&0xffffffff|s>>>11),s=e+(i^(r|~o))+n[8]+0x6fa87e4f&0xffffffff,e=r+(s<<6&0xffffffff|s>>>26),s=o+(r^(e|~i))+n[15]+0xfe2ce6e0&0xffffffff,o=e+(s<<10&0xffffffff|s>>>22),s=i+(e^(o|~r))+n[6]+0xa3014314&0xffffffff,i=o+(s<<15&0xffffffff|s>>>17),s=r+(o^(i|~e))+n[13]+0x4e0811a1&0xffffffff,r=i+(s<<21&0xffffffff|s>>>11),s=e+(i^(r|~o))+n[4]+0xf7537e82&0xffffffff,e=r+(s<<6&0xffffffff|s>>>26),s=o+(r^(e|~i))+n[11]+0xbd3af235&0xffffffff,o=e+(s<<10&0xffffffff|s>>>22),s=i+(e^(o|~r))+n[2]+0x2ad7d2bb&0xffffffff,i=o+(s<<15&0xffffffff|s>>>17),s=r+(o^(i|~e))+n[9]+0xeb86d391&0xffffffff,t.g[0]=t.g[0]+e&0xffffffff,t.g[1]=t.g[1]+(i+(s<<21&0xffffffff|s>>>11))&0xffffffff,t.g[2]=t.g[2]+i&0xffffffff,t.g[3]=t.g[3]+o&0xffffffff}function r(t,e){this.h=e;for(var r=[],n=!0,i=t.length-1;0<=i;i--){var o=0|t[i];n&&o==e||(r[i]=o,n=!1)}this.g=r}!function(t,e){function r(){}r.prototype=e.prototype,t.D=e.prototype,t.prototype=new r,t.prototype.constructor=t,t.C=function(t,r,n){for(var i=Array(arguments.length-2),o=2;o<arguments.length;o++)i[o-2]=arguments[o];return e.prototype[r].apply(t,i)}}(t,function(){this.blockSize=-1}),t.prototype.s=function(){this.g[0]=0x67452301,this.g[1]=0xefcdab89,this.g[2]=0x98badcfe,this.g[3]=0x10325476,this.o=this.h=0},t.prototype.u=function(t,r){void 0===r&&(r=t.length);for(var n=r-this.blockSize,i=this.B,o=this.h,s=0;s<r;){if(0==o)for(;s<=n;)e(this,t,s),s+=this.blockSize;if("string"==typeof t){for(;s<r;)if(i[o++]=t.charCodeAt(s++),o==this.blockSize){e(this,i),o=0;break}}else for(;s<r;)if(i[o++]=t[s++],o==this.blockSize){e(this,i),o=0;break}}this.h=o,this.o+=r},t.prototype.v=function(){var t=Array((56>this.h?this.blockSize:2*this.blockSize)-this.h);t[0]=128;for(var e=1;e<t.length-8;++e)t[e]=0;var r=8*this.o;for(e=t.length-8;e<t.length;++e)t[e]=255&r,r/=256;for(this.u(t),t=Array(16),e=r=0;4>e;++e)for(var n=0;32>n;n+=8)t[r++]=this.g[e]>>>n&255;return t};var o,a={};function f(t){var e;return -128<=t&&128>t?Object.prototype.hasOwnProperty.call(a,t)?a[t]:a[t]=new r([0|(e=t)],0>e?-1:0):new r([0|t],0>t?-1:0)}function h(t){if(isNaN(t)||!isFinite(t))return l;if(0>t)return g(h(-t));for(var e=[],n=1,i=0;t>=n;i++)e[i]=t/n|0,n*=0x100000000;return new r(e,0)}var l=f(0),u=f(1),c=f(0x1000000);function p(t){if(0!=t.h)return!1;for(var e=0;e<t.g.length;e++)if(0!=t.g[e])return!1;return!0}function d(t){return -1==t.h}function g(t){for(var e=t.g.length,n=[],i=0;i<e;i++)n[i]=~t.g[i];return new r(n,~t.h).add(u)}function y(t,e){return t.add(g(e))}function m(t,e){for(;(65535&t[e])!=t[e];)t[e+1]+=t[e]>>>16,t[e]&=65535,e++}function v(t,e){this.g=t,this.h=e}function b(t,e){if(p(e))throw Error("division by zero");if(p(t))return new v(l,l);if(d(t))return e=b(g(t),e),new v(g(e.g),g(e.h));if(d(e))return e=b(t,g(e)),new v(g(e.g),e.h);if(30<t.g.length){if(d(t)||d(e))throw Error("slowDivide_ only works with positive integers.");for(var r=u,n=e;0>=n.l(t);)r=w(r),n=w(n);var i=x(r,1),o=x(n,1);for(n=x(n,2),r=x(r,2);!p(n);){var s=o.add(n);0>=s.l(t)&&(i=i.add(r),o=s),n=x(n,1),r=x(r,1)}return e=y(t,i.j(e)),new v(i,e)}for(i=l;0<=t.l(e);){for(n=48>=(n=Math.ceil(Math.log(r=Math.max(1,Math.floor(t.m()/e.m())))/Math.LN2))?1:Math.pow(2,n-48),s=(o=h(r)).j(e);d(s)||0<s.l(t);)r-=n,s=(o=h(r)).j(e);p(o)&&(o=u),i=i.add(o),t=y(t,s)}return new v(i,t)}function w(t){for(var e=t.g.length+1,n=[],i=0;i<e;i++)n[i]=t.i(i)<<1|t.i(i-1)>>>31;return new r(n,t.h)}function x(t,e){var n=e>>5;e%=32;for(var i=t.g.length-n,o=[],s=0;s<i;s++)o[s]=0<e?t.i(s+n)>>>e|t.i(s+n+1)<<32-e:t.i(s+n);return new r(o,t.h)}(o=r.prototype).m=function(){if(d(this))return-g(this).m();for(var t=0,e=1,r=0;r<this.g.length;r++){var n=this.i(r);t+=(0<=n?n:0x100000000+n)*e,e*=0x100000000}return t},o.toString=function(t){if(2>(t=t||10)||36<t)throw Error("radix out of range: "+t);if(p(this))return"0";if(d(this))return"-"+g(this).toString(t);for(var e=h(Math.pow(t,6)),r=this,n="";;){var i=b(r,e).g,o=((0<(r=y(r,i.j(e))).g.length?r.g[0]:r.h)>>>0).toString(t);if(p(r=i))return o+n;for(;6>o.length;)o="0"+o;n=o+n}},o.i=function(t){return 0>t?0:t<this.g.length?this.g[t]:this.h},o.l=function(t){return d(t=y(this,t))?-1:+!p(t)},o.abs=function(){return d(this)?g(this):this},o.add=function(t){for(var e=Math.max(this.g.length,t.g.length),n=[],i=0,o=0;o<=e;o++){var s=i+(65535&this.i(o))+(65535&t.i(o)),a=(s>>>16)+(this.i(o)>>>16)+(t.i(o)>>>16);i=a>>>16,s&=65535,a&=65535,n[o]=a<<16|s}return new r(n,-0x80000000&n[n.length-1]?-1:0)},o.j=function(t){if(p(this)||p(t))return l;if(d(this))return d(t)?g(this).j(g(t)):g(g(this).j(t));if(d(t))return g(this.j(g(t)));if(0>this.l(c)&&0>t.l(c))return h(this.m()*t.m());for(var e=this.g.length+t.g.length,n=[],i=0;i<2*e;i++)n[i]=0;for(i=0;i<this.g.length;i++)for(var o=0;o<t.g.length;o++){var s=this.i(i)>>>16,a=65535&this.i(i),f=t.i(o)>>>16,u=65535&t.i(o);n[2*i+2*o]+=a*u,m(n,2*i+2*o),n[2*i+2*o+1]+=s*u,m(n,2*i+2*o+1),n[2*i+2*o+1]+=a*f,m(n,2*i+2*o+1),n[2*i+2*o+2]+=s*f,m(n,2*i+2*o+2)}for(i=0;i<e;i++)n[i]=n[2*i+1]<<16|n[2*i];for(i=e;i<2*e;i++)n[i]=0;return new r(n,0)},o.A=function(t){return b(this,t).h},o.and=function(t){for(var e=Math.max(this.g.length,t.g.length),n=[],i=0;i<e;i++)n[i]=this.i(i)&t.i(i);return new r(n,this.h&t.h)},o.or=function(t){for(var e=Math.max(this.g.length,t.g.length),n=[],i=0;i<e;i++)n[i]=this.i(i)|t.i(i);return new r(n,this.h|t.h)},o.xor=function(t){for(var e=Math.max(this.g.length,t.g.length),n=[],i=0;i<e;i++)n[i]=this.i(i)^t.i(i);return new r(n,this.h^t.h)},t.prototype.digest=t.prototype.v,t.prototype.reset=t.prototype.s,t.prototype.update=t.prototype.u,i=s.Md5=t,r.prototype.add=r.prototype.add,r.prototype.multiply=r.prototype.j,r.prototype.modulo=r.prototype.A,r.prototype.compare=r.prototype.l,r.prototype.toNumber=r.prototype.m,r.prototype.toString=r.prototype.toString,r.prototype.getBits=r.prototype.i,r.fromNumber=h,r.fromString=function t(e,r){if(0==e.length)throw Error("number format error: empty string");if(2>(r=r||10)||36<r)throw Error("radix out of range: "+r);if("-"==e.charAt(0))return g(t(e.substring(1),r));if(0<=e.indexOf("-"))throw Error('number format error: interior "-" character');for(var n=h(Math.pow(r,8)),i=l,o=0;o<e.length;o+=8){var s=Math.min(8,e.length-o),a=parseInt(e.substring(o,o+s),r);8>s?(s=h(Math.pow(r,s)),i=i.j(s).add(h(a))):i=(i=i.j(n)).add(h(a))}return i},n=s.Integer=r}).apply(void 0!==o?o:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},10796:(t,e,r)=>{"use strict";var n;r.d(e,{$b:()=>n,Vy:()=>h});let i=[];!function(t){t[t.DEBUG=0]="DEBUG",t[t.VERBOSE=1]="VERBOSE",t[t.INFO=2]="INFO",t[t.WARN=3]="WARN",t[t.ERROR=4]="ERROR",t[t.SILENT=5]="SILENT"}(n||(n={}));let o={debug:n.DEBUG,verbose:n.VERBOSE,info:n.INFO,warn:n.WARN,error:n.ERROR,silent:n.SILENT},s=n.INFO,a={[n.DEBUG]:"log",[n.VERBOSE]:"log",[n.INFO]:"info",[n.WARN]:"warn",[n.ERROR]:"error"},f=(t,e,...r)=>{if(e<t.logLevel)return;let n=new Date().toISOString(),i=a[e];if(i)console[i](`[${n}]  ${t.name}:`,...r);else throw Error(`Attempted to log a message with an invalid logType (value: ${e})`)};class h{constructor(t){this.name=t,this._logLevel=s,this._logHandler=f,this._userLogHandler=null,i.push(this)}get logLevel(){return this._logLevel}set logLevel(t){if(!(t in n))throw TypeError(`Invalid value "${t}" assigned to \`logLevel\``);this._logLevel=t}setLogLevel(t){this._logLevel="string"==typeof t?o[t]:t}get logHandler(){return this._logHandler}set logHandler(t){if("function"!=typeof t)throw TypeError("Value assigned to `logHandler` must be a function");this._logHandler=t}get userLogHandler(){return this._userLogHandler}set userLogHandler(t){this._userLogHandler=t}debug(...t){this._userLogHandler&&this._userLogHandler(this,n.DEBUG,...t),this._logHandler(this,n.DEBUG,...t)}log(...t){this._userLogHandler&&this._userLogHandler(this,n.VERBOSE,...t),this._logHandler(this,n.VERBOSE,...t)}info(...t){this._userLogHandler&&this._userLogHandler(this,n.INFO,...t),this._logHandler(this,n.INFO,...t)}warn(...t){this._userLogHandler&&this._userLogHandler(this,n.WARN,...t),this._logHandler(this,n.WARN,...t)}error(...t){this._userLogHandler&&this._userLogHandler(this,n.ERROR,...t),this._logHandler(this,n.ERROR,...t)}}},16203:(t,e,r)=>{"use strict";r.d(e,{HF:()=>n.Y,eJ:()=>n.ab,xI:()=>n.p,hg:()=>n.z,x9:()=>n.ac,df:()=>n.d,CI:()=>n.D,r7:()=>n.al});var n=r(24917);r(46235),r(49887),r(10796),r(56391)},19946:(t,e,r)=>{"use strict";r.d(e,{A:()=>f});var n=r(12115);let i=t=>t.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),o=function(){for(var t=arguments.length,e=Array(t),r=0;r<t;r++)e[r]=arguments[r];return e.filter((t,e,r)=>!!t&&""!==t.trim()&&r.indexOf(t)===e).join(" ").trim()};var s={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let a=(0,n.forwardRef)((t,e)=>{let{color:r="currentColor",size:i=24,strokeWidth:a=2,absoluteStrokeWidth:f,className:h="",children:l,iconNode:u,...c}=t;return(0,n.createElement)("svg",{ref:e,...s,width:i,height:i,stroke:r,strokeWidth:f?24*Number(a)/Number(i):a,className:o("lucide",h),...c},[...u.map(t=>{let[e,r]=t;return(0,n.createElement)(e,r)}),...Array.isArray(l)?l:[l]])}),f=(t,e)=>{let r=(0,n.forwardRef)((r,s)=>{let{className:f,...h}=r;return(0,n.createElement)(a,{ref:s,iconNode:e,className:o("lucide-".concat(i(t)),f),...h})});return r.displayName="".concat(t),r}},23915:(t,e,r)=>{"use strict";r.d(e,{Dk:()=>n.Dk,Wp:()=>n.Wp});var n=r(46235);(0,n.KO)("firebase","11.8.1","app")},35317:(t,e,r)=>{"use strict";r.d(e,{BN:()=>n.BN,H9:()=>n.H9,My:()=>n.My,O5:()=>n.O5,P:()=>n.P,_M:()=>n._M,aQ:()=>n.aQ,aU:()=>n.aU,gS:()=>n.gS,mZ:()=>n.mZ,rJ:()=>n.rJ,x7:()=>n.x7});var n=r(87015)},39249:(t,e,r)=>{"use strict";r.d(e,{Cl:()=>n,Tt:()=>i,fX:()=>o});var n=function(){return(n=Object.assign||function(t){for(var e,r=1,n=arguments.length;r<n;r++)for(var i in e=arguments[r])Object.prototype.hasOwnProperty.call(e,i)&&(t[i]=e[i]);return t}).apply(this,arguments)};function i(t,e){var r={};for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&0>e.indexOf(n)&&(r[n]=t[n]);if(null!=t&&"function"==typeof Object.getOwnPropertySymbols)for(var i=0,n=Object.getOwnPropertySymbols(t);i<n.length;i++)0>e.indexOf(n[i])&&Object.prototype.propertyIsEnumerable.call(t,n[i])&&(r[n[i]]=t[n[i]]);return r}Object.create;function o(t,e,r){if(r||2==arguments.length)for(var n,i=0,o=e.length;i<o;i++)!n&&i in e||(n||(n=Array.prototype.slice.call(e,0,i)),n[i]=e[i]);return t.concat(n||Array.prototype.slice.call(e))}Object.create,"function"==typeof SuppressedError&&SuppressedError},46235:(t,e,r)=>{"use strict";r.d(e,{Dk:()=>C,KO:()=>T,MF:()=>A,Sx:()=>S,Wp:()=>I,j6:()=>v,om:()=>m,xZ:()=>b});var n=r(56391),i=r(10796),o=r(49887),s=r(46984);class a{constructor(t){this.container=t}getPlatformInfoString(){return this.container.getProviders().map(t=>{if(!function(t){let e=t.getComponent();return(null==e?void 0:e.type)==="VERSION"}(t))return null;{let e=t.getImmediate();return`${e.library}/${e.version}`}}).filter(t=>t).join(" ")}}let f="@firebase/app",h="0.13.0",l=new i.Vy("@firebase/app"),u="[DEFAULT]",c={[f]:"fire-core","@firebase/app-compat":"fire-core-compat","@firebase/analytics":"fire-analytics","@firebase/analytics-compat":"fire-analytics-compat","@firebase/app-check":"fire-app-check","@firebase/app-check-compat":"fire-app-check-compat","@firebase/auth":"fire-auth","@firebase/auth-compat":"fire-auth-compat","@firebase/database":"fire-rtdb","@firebase/data-connect":"fire-data-connect","@firebase/database-compat":"fire-rtdb-compat","@firebase/functions":"fire-fn","@firebase/functions-compat":"fire-fn-compat","@firebase/installations":"fire-iid","@firebase/installations-compat":"fire-iid-compat","@firebase/messaging":"fire-fcm","@firebase/messaging-compat":"fire-fcm-compat","@firebase/performance":"fire-perf","@firebase/performance-compat":"fire-perf-compat","@firebase/remote-config":"fire-rc","@firebase/remote-config-compat":"fire-rc-compat","@firebase/storage":"fire-gcs","@firebase/storage-compat":"fire-gcs-compat","@firebase/firestore":"fire-fst","@firebase/firestore-compat":"fire-fst-compat","@firebase/ai":"fire-vertex","fire-js":"fire-js",firebase:"fire-js-all"},p=new Map,d=new Map,g=new Map;function y(t,e){try{t.container.addComponent(e)}catch(r){l.debug(`Component ${e.name} failed to register with FirebaseApp ${t.name}`,r)}}function m(t){let e=t.name;if(g.has(e))return l.debug(`There were multiple attempts to register component ${e}.`),!1;for(let r of(g.set(e,t),p.values()))y(r,t);for(let e of d.values())y(e,t);return!0}function v(t,e){let r=t.container.getProvider("heartbeat").getImmediate({optional:!0});return r&&r.triggerHeartbeat(),t.container.getProvider(e)}function b(t){return null!=t&&void 0!==t.settings}let w=new o.FA("app","Firebase",{"no-app":"No Firebase App '{$appName}' has been created - call initializeApp() first","bad-app-name":"Illegal App name: '{$appName}'","duplicate-app":"Firebase App named '{$appName}' already exists with different options or config","app-deleted":"Firebase App named '{$appName}' already deleted","server-app-deleted":"Firebase Server App has been deleted","no-options":"Need to provide options, when not being deployed to hosting via source.","invalid-app-argument":"firebase.{$appName}() takes either no argument or a Firebase App instance.","invalid-log-argument":"First argument to `onLog` must be null or a function.","idb-open":"Error thrown when opening IndexedDB. Original error: {$originalErrorMessage}.","idb-get":"Error thrown when reading from IndexedDB. Original error: {$originalErrorMessage}.","idb-set":"Error thrown when writing to IndexedDB. Original error: {$originalErrorMessage}.","idb-delete":"Error thrown when deleting from IndexedDB. Original error: {$originalErrorMessage}.","finalization-registry-not-supported":"FirebaseServerApp deleteOnDeref field defined but the JS runtime does not support FinalizationRegistry.","invalid-server-app-environment":"FirebaseServerApp is not for use in browser environments."});class x{constructor(t,e,r){this._isDeleted=!1,this._options=Object.assign({},t),this._config=Object.assign({},e),this._name=e.name,this._automaticDataCollectionEnabled=e.automaticDataCollectionEnabled,this._container=r,this.container.addComponent(new n.uA("app",()=>this,"PUBLIC"))}get automaticDataCollectionEnabled(){return this.checkDestroyed(),this._automaticDataCollectionEnabled}set automaticDataCollectionEnabled(t){this.checkDestroyed(),this._automaticDataCollectionEnabled=t}get name(){return this.checkDestroyed(),this._name}get options(){return this.checkDestroyed(),this._options}get config(){return this.checkDestroyed(),this._config}get container(){return this._container}get isDeleted(){return this._isDeleted}set isDeleted(t){this._isDeleted=t}checkDestroyed(){if(this.isDeleted)throw w.create("app-deleted",{appName:this._name})}}function E(t,e){let r=(0,o.u)(t.split(".")[1]);if(null===r){console.error(`FirebaseServerApp ${e} is invalid: second part could not be parsed.`);return}if(void 0===JSON.parse(r).exp){console.error(`FirebaseServerApp ${e} is invalid: expiration claim could not be parsed`);return}let n=1e3*JSON.parse(r).exp;n-new Date().getTime()<=0&&console.error(`FirebaseServerApp ${e} is invalid: the token has expired.`)}let A="11.8.0";function I(t,e={}){let r=t;"object"!=typeof e&&(e={name:e});let i=Object.assign({name:u,automaticDataCollectionEnabled:!0},e),s=i.name;if("string"!=typeof s||!s)throw w.create("bad-app-name",{appName:String(s)});if(r||(r=(0,o.T9)()),!r)throw w.create("no-options");let a=p.get(s);if(a){if((0,o.bD)(r,a.options)&&(0,o.bD)(i,a.config))return a;throw w.create("duplicate-app",{appName:s})}let f=new n.h1(s);for(let t of g.values())f.addComponent(t);let h=new x(r,i,f);return p.set(s,h),h}function S(t=u){let e=p.get(t);if(!e&&t===u&&(0,o.T9)())return I();if(!e)throw w.create("no-app",{appName:t});return e}function C(){return Array.from(p.values())}async function _(t){let e=!1,r=t.name;p.has(r)?(e=!0,p.delete(r)):d.has(r)&&0>=t.decRefCount()&&(d.delete(r),e=!0),e&&(await Promise.all(t.container.getProviders().map(t=>t.delete())),t.isDeleted=!0)}function T(t,e,r){var i;let o=null!==(i=c[t])&&void 0!==i?i:t;r&&(o+=`-${r}`);let s=o.match(/\s|\//),a=e.match(/\s|\//);if(s||a){let t=[`Unable to register library "${o}" with version "${e}":`];s&&t.push(`library name "${o}" contains illegal characters (whitespace or "/")`),s&&a&&t.push("and"),a&&t.push(`version name "${e}" contains illegal characters (whitespace or "/")`),l.warn(t.join(" "));return}m(new n.uA(`${o}-version`,()=>({library:o,version:e}),"VERSION"))}let O="firebase-heartbeat-store",D=null;function j(){return D||(D=(0,s.P2)("firebase-heartbeat-database",1,{upgrade:(t,e)=>{if(0===e)try{t.createObjectStore(O)}catch(t){console.warn(t)}}}).catch(t=>{throw w.create("idb-open",{originalErrorMessage:t.message})})),D}async function k(t){try{let e=(await j()).transaction(O),r=await e.objectStore(O).get(M(t));return await e.done,r}catch(t){if(t instanceof o.g)l.warn(t.message);else{let e=w.create("idb-get",{originalErrorMessage:null==t?void 0:t.message});l.warn(e.message)}}}async function B(t,e){try{let r=(await j()).transaction(O,"readwrite"),n=r.objectStore(O);await n.put(e,M(t)),await r.done}catch(t){if(t instanceof o.g)l.warn(t.message);else{let e=w.create("idb-set",{originalErrorMessage:null==t?void 0:t.message});l.warn(e.message)}}}function M(t){return`${t.name}!${t.options.appId}`}class P{constructor(t){this.container=t,this._heartbeatsCache=null;let e=this.container.getProvider("app").getImmediate();this._storage=new R(e),this._heartbeatsCachePromise=this._storage.read().then(t=>(this._heartbeatsCache=t,t))}async triggerHeartbeat(){var t,e;try{let r=this.container.getProvider("platform-logger").getImmediate().getPlatformInfoString(),n=L();if((null===(t=this._heartbeatsCache)||void 0===t?void 0:t.heartbeats)==null&&(this._heartbeatsCache=await this._heartbeatsCachePromise,(null===(e=this._heartbeatsCache)||void 0===e?void 0:e.heartbeats)==null)||this._heartbeatsCache.lastSentHeartbeatDate===n||this._heartbeatsCache.heartbeats.some(t=>t.date===n))return;if(this._heartbeatsCache.heartbeats.push({date:n,agent:r}),this._heartbeatsCache.heartbeats.length>30){let t=function(t){if(0===t.length)return -1;let e=0,r=t[0].date;for(let n=1;n<t.length;n++)t[n].date<r&&(r=t[n].date,e=n);return e}(this._heartbeatsCache.heartbeats);this._heartbeatsCache.heartbeats.splice(t,1)}return this._storage.overwrite(this._heartbeatsCache)}catch(t){l.warn(t)}}async getHeartbeatsHeader(){var t;try{if(null===this._heartbeatsCache&&await this._heartbeatsCachePromise,(null===(t=this._heartbeatsCache)||void 0===t?void 0:t.heartbeats)==null||0===this._heartbeatsCache.heartbeats.length)return"";let e=L(),{heartbeatsToSend:r,unsentEntries:n}=function(t,e=1024){let r=[],n=t.slice();for(let i of t){let t=r.find(t=>t.agent===i.agent);if(t){if(t.dates.push(i.date),N(r)>e){t.dates.pop();break}}else if(r.push({agent:i.agent,dates:[i.date]}),N(r)>e){r.pop();break}n=n.slice(1)}return{heartbeatsToSend:r,unsentEntries:n}}(this._heartbeatsCache.heartbeats),i=(0,o.Uj)(JSON.stringify({version:2,heartbeats:r}));return this._heartbeatsCache.lastSentHeartbeatDate=e,n.length>0?(this._heartbeatsCache.heartbeats=n,await this._storage.overwrite(this._heartbeatsCache)):(this._heartbeatsCache.heartbeats=[],this._storage.overwrite(this._heartbeatsCache)),i}catch(t){return l.warn(t),""}}}function L(){return new Date().toISOString().substring(0,10)}class R{constructor(t){this.app=t,this._canUseIndexedDBPromise=this.runIndexedDBEnvironmentCheck()}async runIndexedDBEnvironmentCheck(){return!!(0,o.zW)()&&(0,o.eX)().then(()=>!0).catch(()=>!1)}async read(){if(!await this._canUseIndexedDBPromise)return{heartbeats:[]};{let t=await k(this.app);return(null==t?void 0:t.heartbeats)?t:{heartbeats:[]}}}async overwrite(t){var e;if(await this._canUseIndexedDBPromise){let r=await this.read();return B(this.app,{lastSentHeartbeatDate:null!==(e=t.lastSentHeartbeatDate)&&void 0!==e?e:r.lastSentHeartbeatDate,heartbeats:t.heartbeats})}}async add(t){var e;if(await this._canUseIndexedDBPromise){let r=await this.read();return B(this.app,{lastSentHeartbeatDate:null!==(e=t.lastSentHeartbeatDate)&&void 0!==e?e:r.lastSentHeartbeatDate,heartbeats:[...r.heartbeats,...t.heartbeats]})}}}function N(t){return(0,o.Uj)(JSON.stringify({version:2,heartbeats:t})).length}m(new n.uA("platform-logger",t=>new a(t),"PRIVATE")),m(new n.uA("heartbeat",t=>new P(t),"PRIVATE")),T(f,h,""),T(f,h,"esm2017"),T("fire-js","")},46984:(t,e,r)=>{"use strict";let n,i;r.d(e,{P2:()=>d});let o=(t,e)=>e.some(e=>t instanceof e),s=new WeakMap,a=new WeakMap,f=new WeakMap,h=new WeakMap,l=new WeakMap,u={get(t,e,r){if(t instanceof IDBTransaction){if("done"===e)return a.get(t);if("objectStoreNames"===e)return t.objectStoreNames||f.get(t);if("store"===e)return r.objectStoreNames[1]?void 0:r.objectStore(r.objectStoreNames[0])}return c(t[e])},set:(t,e,r)=>(t[e]=r,!0),has:(t,e)=>t instanceof IDBTransaction&&("done"===e||"store"===e)||e in t};function c(t){if(t instanceof IDBRequest)return function(t){let e=new Promise((e,r)=>{let n=()=>{t.removeEventListener("success",i),t.removeEventListener("error",o)},i=()=>{e(c(t.result)),n()},o=()=>{r(t.error),n()};t.addEventListener("success",i),t.addEventListener("error",o)});return e.then(e=>{e instanceof IDBCursor&&s.set(e,t)}).catch(()=>{}),l.set(e,t),e}(t);if(h.has(t))return h.get(t);let e=function(t){if("function"==typeof t)return t!==IDBDatabase.prototype.transaction||"objectStoreNames"in IDBTransaction.prototype?(i||(i=[IDBCursor.prototype.advance,IDBCursor.prototype.continue,IDBCursor.prototype.continuePrimaryKey])).includes(t)?function(...e){return t.apply(p(this),e),c(s.get(this))}:function(...e){return c(t.apply(p(this),e))}:function(e,...r){let n=t.call(p(this),e,...r);return f.set(n,e.sort?e.sort():[e]),c(n)};return(t instanceof IDBTransaction&&function(t){if(a.has(t))return;let e=new Promise((e,r)=>{let n=()=>{t.removeEventListener("complete",i),t.removeEventListener("error",o),t.removeEventListener("abort",o)},i=()=>{e(),n()},o=()=>{r(t.error||new DOMException("AbortError","AbortError")),n()};t.addEventListener("complete",i),t.addEventListener("error",o),t.addEventListener("abort",o)});a.set(t,e)}(t),o(t,n||(n=[IDBDatabase,IDBObjectStore,IDBIndex,IDBCursor,IDBTransaction])))?new Proxy(t,u):t}(t);return e!==t&&(h.set(t,e),l.set(e,t)),e}let p=t=>l.get(t);function d(t,e,{blocked:r,upgrade:n,blocking:i,terminated:o}={}){let s=indexedDB.open(t,e),a=c(s);return n&&s.addEventListener("upgradeneeded",t=>{n(c(s.result),t.oldVersion,t.newVersion,c(s.transaction),t)}),r&&s.addEventListener("blocked",t=>r(t.oldVersion,t.newVersion,t)),a.then(t=>{o&&t.addEventListener("close",()=>o()),i&&t.addEventListener("versionchange",t=>i(t.oldVersion,t.newVersion,t))}).catch(()=>{}),a}let g=["get","getKey","getAll","getAllKeys","count"],y=["put","add","delete","clear"],m=new Map;function v(t,e){if(!(t instanceof IDBDatabase&&!(e in t)&&"string"==typeof e))return;if(m.get(e))return m.get(e);let r=e.replace(/FromIndex$/,""),n=e!==r,i=y.includes(r);if(!(r in(n?IDBIndex:IDBObjectStore).prototype)||!(i||g.includes(r)))return;let o=async function(t,...e){let o=this.transaction(t,i?"readwrite":"readonly"),s=o.store;return n&&(s=s.index(e.shift())),(await Promise.all([s[r](...e),i&&o.done]))[0]};return m.set(e,o),o}u=(t=>({...t,get:(e,r,n)=>v(e,r)||t.get(e,r,n),has:(e,r)=>!!v(e,r)||t.has(e,r)}))(u)},49641:t=>{!function(){var e={675:function(t,e){"use strict";e.byteLength=function(t){var e=f(t),r=e[0],n=e[1];return(r+n)*3/4-n},e.toByteArray=function(t){var e,r,o=f(t),s=o[0],a=o[1],h=new i((s+a)*3/4-a),l=0,u=a>0?s-4:s;for(r=0;r<u;r+=4)e=n[t.charCodeAt(r)]<<18|n[t.charCodeAt(r+1)]<<12|n[t.charCodeAt(r+2)]<<6|n[t.charCodeAt(r+3)],h[l++]=e>>16&255,h[l++]=e>>8&255,h[l++]=255&e;return 2===a&&(e=n[t.charCodeAt(r)]<<2|n[t.charCodeAt(r+1)]>>4,h[l++]=255&e),1===a&&(e=n[t.charCodeAt(r)]<<10|n[t.charCodeAt(r+1)]<<4|n[t.charCodeAt(r+2)]>>2,h[l++]=e>>8&255,h[l++]=255&e),h},e.fromByteArray=function(t){for(var e,n=t.length,i=n%3,o=[],s=0,a=n-i;s<a;s+=16383)o.push(function(t,e,n){for(var i,o=[],s=e;s<n;s+=3)i=(t[s]<<16&0xff0000)+(t[s+1]<<8&65280)+(255&t[s+2]),o.push(r[i>>18&63]+r[i>>12&63]+r[i>>6&63]+r[63&i]);return o.join("")}(t,s,s+16383>a?a:s+16383));return 1===i?o.push(r[(e=t[n-1])>>2]+r[e<<4&63]+"=="):2===i&&o.push(r[(e=(t[n-2]<<8)+t[n-1])>>10]+r[e>>4&63]+r[e<<2&63]+"="),o.join("")};for(var r=[],n=[],i="undefined"!=typeof Uint8Array?Uint8Array:Array,o="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",s=0,a=o.length;s<a;++s)r[s]=o[s],n[o.charCodeAt(s)]=s;function f(t){var e=t.length;if(e%4>0)throw Error("Invalid string. Length must be a multiple of 4");var r=t.indexOf("=");-1===r&&(r=e);var n=r===e?0:4-r%4;return[r,n]}n["-".charCodeAt(0)]=62,n["_".charCodeAt(0)]=63},72:function(t,e,r){"use strict";var n=r(675),i=r(783),o="function"==typeof Symbol&&"function"==typeof Symbol.for?Symbol.for("nodejs.util.inspect.custom"):null;function s(t){if(t>0x7fffffff)throw RangeError('The value "'+t+'" is invalid for option "size"');var e=new Uint8Array(t);return Object.setPrototypeOf(e,a.prototype),e}function a(t,e,r){if("number"==typeof t){if("string"==typeof e)throw TypeError('The "string" argument must be of type string. Received type number');return l(t)}return f(t,e,r)}function f(t,e,r){if("string"==typeof t)return function(t,e){if(("string"!=typeof e||""===e)&&(e="utf8"),!a.isEncoding(e))throw TypeError("Unknown encoding: "+e);var r=0|p(t,e),n=s(r),i=n.write(t,e);return i!==r&&(n=n.slice(0,i)),n}(t,e);if(ArrayBuffer.isView(t))return u(t);if(null==t)throw TypeError("The first argument must be one of type string, Buffer, ArrayBuffer, Array, or Array-like Object. Received type "+typeof t);if(O(t,ArrayBuffer)||t&&O(t.buffer,ArrayBuffer)||"undefined"!=typeof SharedArrayBuffer&&(O(t,SharedArrayBuffer)||t&&O(t.buffer,SharedArrayBuffer)))return function(t,e,r){var n;if(e<0||t.byteLength<e)throw RangeError('"offset" is outside of buffer bounds');if(t.byteLength<e+(r||0))throw RangeError('"length" is outside of buffer bounds');return Object.setPrototypeOf(n=void 0===e&&void 0===r?new Uint8Array(t):void 0===r?new Uint8Array(t,e):new Uint8Array(t,e,r),a.prototype),n}(t,e,r);if("number"==typeof t)throw TypeError('The "value" argument must not be of type number. Received type number');var n=t.valueOf&&t.valueOf();if(null!=n&&n!==t)return a.from(n,e,r);var i=function(t){if(a.isBuffer(t)){var e=0|c(t.length),r=s(e);return 0===r.length||t.copy(r,0,0,e),r}return void 0!==t.length?"number"!=typeof t.length||function(t){return t!=t}(t.length)?s(0):u(t):"Buffer"===t.type&&Array.isArray(t.data)?u(t.data):void 0}(t);if(i)return i;if("undefined"!=typeof Symbol&&null!=Symbol.toPrimitive&&"function"==typeof t[Symbol.toPrimitive])return a.from(t[Symbol.toPrimitive]("string"),e,r);throw TypeError("The first argument must be one of type string, Buffer, ArrayBuffer, Array, or Array-like Object. Received type "+typeof t)}function h(t){if("number"!=typeof t)throw TypeError('"size" argument must be of type number');if(t<0)throw RangeError('The value "'+t+'" is invalid for option "size"')}function l(t){return h(t),s(t<0?0:0|c(t))}function u(t){for(var e=t.length<0?0:0|c(t.length),r=s(e),n=0;n<e;n+=1)r[n]=255&t[n];return r}e.Buffer=a,e.SlowBuffer=function(t){return+t!=t&&(t=0),a.alloc(+t)},e.INSPECT_MAX_BYTES=50,e.kMaxLength=0x7fffffff,a.TYPED_ARRAY_SUPPORT=function(){try{var t=new Uint8Array(1),e={foo:function(){return 42}};return Object.setPrototypeOf(e,Uint8Array.prototype),Object.setPrototypeOf(t,e),42===t.foo()}catch(t){return!1}}(),a.TYPED_ARRAY_SUPPORT||"undefined"==typeof console||"function"!=typeof console.error||console.error("This browser lacks typed array (Uint8Array) support which is required by `buffer` v5.x. Use `buffer` v4.x if you require old browser support."),Object.defineProperty(a.prototype,"parent",{enumerable:!0,get:function(){if(a.isBuffer(this))return this.buffer}}),Object.defineProperty(a.prototype,"offset",{enumerable:!0,get:function(){if(a.isBuffer(this))return this.byteOffset}}),a.poolSize=8192,a.from=function(t,e,r){return f(t,e,r)},Object.setPrototypeOf(a.prototype,Uint8Array.prototype),Object.setPrototypeOf(a,Uint8Array),a.alloc=function(t,e,r){return(h(t),t<=0)?s(t):void 0!==e?"string"==typeof r?s(t).fill(e,r):s(t).fill(e):s(t)},a.allocUnsafe=function(t){return l(t)},a.allocUnsafeSlow=function(t){return l(t)};function c(t){if(t>=0x7fffffff)throw RangeError("Attempt to allocate Buffer larger than maximum size: 0x7fffffff bytes");return 0|t}function p(t,e){if(a.isBuffer(t))return t.length;if(ArrayBuffer.isView(t)||O(t,ArrayBuffer))return t.byteLength;if("string"!=typeof t)throw TypeError('The "string" argument must be one of type string, Buffer, or ArrayBuffer. Received type '+typeof t);var r=t.length,n=arguments.length>2&&!0===arguments[2];if(!n&&0===r)return 0;for(var i=!1;;)switch(e){case"ascii":case"latin1":case"binary":return r;case"utf8":case"utf-8":return S(t).length;case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return 2*r;case"hex":return r>>>1;case"base64":return _(t).length;default:if(i)return n?-1:S(t).length;e=(""+e).toLowerCase(),i=!0}}function d(t,e,r){var i,o,s,a=!1;if((void 0===e||e<0)&&(e=0),e>this.length||((void 0===r||r>this.length)&&(r=this.length),r<=0||(r>>>=0)<=(e>>>=0)))return"";for(t||(t="utf8");;)switch(t){case"hex":return function(t,e,r){var n=t.length;(!e||e<0)&&(e=0),(!r||r<0||r>n)&&(r=n);for(var i="",o=e;o<r;++o)i+=D[t[o]];return i}(this,e,r);case"utf8":case"utf-8":return v(this,e,r);case"ascii":return function(t,e,r){var n="";r=Math.min(t.length,r);for(var i=e;i<r;++i)n+=String.fromCharCode(127&t[i]);return n}(this,e,r);case"latin1":case"binary":return function(t,e,r){var n="";r=Math.min(t.length,r);for(var i=e;i<r;++i)n+=String.fromCharCode(t[i]);return n}(this,e,r);case"base64":return i=this,o=e,s=r,0===o&&s===i.length?n.fromByteArray(i):n.fromByteArray(i.slice(o,s));case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return function(t,e,r){for(var n=t.slice(e,r),i="",o=0;o<n.length;o+=2)i+=String.fromCharCode(n[o]+256*n[o+1]);return i}(this,e,r);default:if(a)throw TypeError("Unknown encoding: "+t);t=(t+"").toLowerCase(),a=!0}}function g(t,e,r){var n=t[e];t[e]=t[r],t[r]=n}function y(t,e,r,n,i){var o;if(0===t.length)return -1;if("string"==typeof r?(n=r,r=0):r>0x7fffffff?r=0x7fffffff:r<-0x80000000&&(r=-0x80000000),(o=r*=1)!=o&&(r=i?0:t.length-1),r<0&&(r=t.length+r),r>=t.length){if(i)return -1;r=t.length-1}else if(r<0){if(!i)return -1;r=0}if("string"==typeof e&&(e=a.from(e,n)),a.isBuffer(e))return 0===e.length?-1:m(t,e,r,n,i);if("number"==typeof e)return(e&=255,"function"==typeof Uint8Array.prototype.indexOf)?i?Uint8Array.prototype.indexOf.call(t,e,r):Uint8Array.prototype.lastIndexOf.call(t,e,r):m(t,[e],r,n,i);throw TypeError("val must be string, number or Buffer")}function m(t,e,r,n,i){var o,s=1,a=t.length,f=e.length;if(void 0!==n&&("ucs2"===(n=String(n).toLowerCase())||"ucs-2"===n||"utf16le"===n||"utf-16le"===n)){if(t.length<2||e.length<2)return -1;s=2,a/=2,f/=2,r/=2}function h(t,e){return 1===s?t[e]:t.readUInt16BE(e*s)}if(i){var l=-1;for(o=r;o<a;o++)if(h(t,o)===h(e,-1===l?0:o-l)){if(-1===l&&(l=o),o-l+1===f)return l*s}else -1!==l&&(o-=o-l),l=-1}else for(r+f>a&&(r=a-f),o=r;o>=0;o--){for(var u=!0,c=0;c<f;c++)if(h(t,o+c)!==h(e,c)){u=!1;break}if(u)return o}return -1}a.isBuffer=function(t){return null!=t&&!0===t._isBuffer&&t!==a.prototype},a.compare=function(t,e){if(O(t,Uint8Array)&&(t=a.from(t,t.offset,t.byteLength)),O(e,Uint8Array)&&(e=a.from(e,e.offset,e.byteLength)),!a.isBuffer(t)||!a.isBuffer(e))throw TypeError('The "buf1", "buf2" arguments must be one of type Buffer or Uint8Array');if(t===e)return 0;for(var r=t.length,n=e.length,i=0,o=Math.min(r,n);i<o;++i)if(t[i]!==e[i]){r=t[i],n=e[i];break}return r<n?-1:+(n<r)},a.isEncoding=function(t){switch(String(t).toLowerCase()){case"hex":case"utf8":case"utf-8":case"ascii":case"latin1":case"binary":case"base64":case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return!0;default:return!1}},a.concat=function(t,e){if(!Array.isArray(t))throw TypeError('"list" argument must be an Array of Buffers');if(0===t.length)return a.alloc(0);if(void 0===e)for(r=0,e=0;r<t.length;++r)e+=t[r].length;var r,n=a.allocUnsafe(e),i=0;for(r=0;r<t.length;++r){var o=t[r];if(O(o,Uint8Array)&&(o=a.from(o)),!a.isBuffer(o))throw TypeError('"list" argument must be an Array of Buffers');o.copy(n,i),i+=o.length}return n},a.byteLength=p,a.prototype._isBuffer=!0,a.prototype.swap16=function(){var t=this.length;if(t%2!=0)throw RangeError("Buffer size must be a multiple of 16-bits");for(var e=0;e<t;e+=2)g(this,e,e+1);return this},a.prototype.swap32=function(){var t=this.length;if(t%4!=0)throw RangeError("Buffer size must be a multiple of 32-bits");for(var e=0;e<t;e+=4)g(this,e,e+3),g(this,e+1,e+2);return this},a.prototype.swap64=function(){var t=this.length;if(t%8!=0)throw RangeError("Buffer size must be a multiple of 64-bits");for(var e=0;e<t;e+=8)g(this,e,e+7),g(this,e+1,e+6),g(this,e+2,e+5),g(this,e+3,e+4);return this},a.prototype.toString=function(){var t=this.length;return 0===t?"":0==arguments.length?v(this,0,t):d.apply(this,arguments)},a.prototype.toLocaleString=a.prototype.toString,a.prototype.equals=function(t){if(!a.isBuffer(t))throw TypeError("Argument must be a Buffer");return this===t||0===a.compare(this,t)},a.prototype.inspect=function(){var t="",r=e.INSPECT_MAX_BYTES;return t=this.toString("hex",0,r).replace(/(.{2})/g,"$1 ").trim(),this.length>r&&(t+=" ... "),"<Buffer "+t+">"},o&&(a.prototype[o]=a.prototype.inspect),a.prototype.compare=function(t,e,r,n,i){if(O(t,Uint8Array)&&(t=a.from(t,t.offset,t.byteLength)),!a.isBuffer(t))throw TypeError('The "target" argument must be one of type Buffer or Uint8Array. Received type '+typeof t);if(void 0===e&&(e=0),void 0===r&&(r=t?t.length:0),void 0===n&&(n=0),void 0===i&&(i=this.length),e<0||r>t.length||n<0||i>this.length)throw RangeError("out of range index");if(n>=i&&e>=r)return 0;if(n>=i)return -1;if(e>=r)return 1;if(e>>>=0,r>>>=0,n>>>=0,i>>>=0,this===t)return 0;for(var o=i-n,s=r-e,f=Math.min(o,s),h=this.slice(n,i),l=t.slice(e,r),u=0;u<f;++u)if(h[u]!==l[u]){o=h[u],s=l[u];break}return o<s?-1:+(s<o)},a.prototype.includes=function(t,e,r){return -1!==this.indexOf(t,e,r)},a.prototype.indexOf=function(t,e,r){return y(this,t,e,r,!0)},a.prototype.lastIndexOf=function(t,e,r){return y(this,t,e,r,!1)};function v(t,e,r){r=Math.min(t.length,r);for(var n=[],i=e;i<r;){var o,s,a,f,h=t[i],l=null,u=h>239?4:h>223?3:h>191?2:1;if(i+u<=r)switch(u){case 1:h<128&&(l=h);break;case 2:(192&(o=t[i+1]))==128&&(f=(31&h)<<6|63&o)>127&&(l=f);break;case 3:o=t[i+1],s=t[i+2],(192&o)==128&&(192&s)==128&&(f=(15&h)<<12|(63&o)<<6|63&s)>2047&&(f<55296||f>57343)&&(l=f);break;case 4:o=t[i+1],s=t[i+2],a=t[i+3],(192&o)==128&&(192&s)==128&&(192&a)==128&&(f=(15&h)<<18|(63&o)<<12|(63&s)<<6|63&a)>65535&&f<1114112&&(l=f)}null===l?(l=65533,u=1):l>65535&&(l-=65536,n.push(l>>>10&1023|55296),l=56320|1023&l),n.push(l),i+=u}return function(t){var e=t.length;if(e<=4096)return String.fromCharCode.apply(String,t);for(var r="",n=0;n<e;)r+=String.fromCharCode.apply(String,t.slice(n,n+=4096));return r}(n)}function b(t,e,r){if(t%1!=0||t<0)throw RangeError("offset is not uint");if(t+e>r)throw RangeError("Trying to access beyond buffer length")}function w(t,e,r,n,i,o){if(!a.isBuffer(t))throw TypeError('"buffer" argument must be a Buffer instance');if(e>i||e<o)throw RangeError('"value" argument is out of bounds');if(r+n>t.length)throw RangeError("Index out of range")}function x(t,e,r,n,i,o){if(r+n>t.length||r<0)throw RangeError("Index out of range")}function E(t,e,r,n,o){return e*=1,r>>>=0,o||x(t,e,r,4,34028234663852886e22,-34028234663852886e22),i.write(t,e,r,n,23,4),r+4}function A(t,e,r,n,o){return e*=1,r>>>=0,o||x(t,e,r,8,17976931348623157e292,-17976931348623157e292),i.write(t,e,r,n,52,8),r+8}a.prototype.write=function(t,e,r,n){if(void 0===e)n="utf8",r=this.length,e=0;else if(void 0===r&&"string"==typeof e)n=e,r=this.length,e=0;else if(isFinite(e))e>>>=0,isFinite(r)?(r>>>=0,void 0===n&&(n="utf8")):(n=r,r=void 0);else throw Error("Buffer.write(string, encoding, offset[, length]) is no longer supported");var i,o,s,a,f,h,l,u,c=this.length-e;if((void 0===r||r>c)&&(r=c),t.length>0&&(r<0||e<0)||e>this.length)throw RangeError("Attempt to write outside buffer bounds");n||(n="utf8");for(var p=!1;;)switch(n){case"hex":return function(t,e,r,n){r=Number(r)||0;var i=t.length-r;n?(n=Number(n))>i&&(n=i):n=i;var o=e.length;n>o/2&&(n=o/2);for(var s=0;s<n;++s){var a,f=parseInt(e.substr(2*s,2),16);if((a=f)!=a)break;t[r+s]=f}return s}(this,t,e,r);case"utf8":case"utf-8":return i=e,o=r,T(S(t,this.length-i),this,i,o);case"ascii":return s=e,a=r,T(C(t),this,s,a);case"latin1":case"binary":return function(t,e,r,n){return T(C(e),t,r,n)}(this,t,e,r);case"base64":return f=e,h=r,T(_(t),this,f,h);case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return l=e,u=r,T(function(t,e){for(var r,n,i=[],o=0;o<t.length&&!((e-=2)<0);++o)n=(r=t.charCodeAt(o))>>8,i.push(r%256),i.push(n);return i}(t,this.length-l),this,l,u);default:if(p)throw TypeError("Unknown encoding: "+n);n=(""+n).toLowerCase(),p=!0}},a.prototype.toJSON=function(){return{type:"Buffer",data:Array.prototype.slice.call(this._arr||this,0)}},a.prototype.slice=function(t,e){var r=this.length;t=~~t,e=void 0===e?r:~~e,t<0?(t+=r)<0&&(t=0):t>r&&(t=r),e<0?(e+=r)<0&&(e=0):e>r&&(e=r),e<t&&(e=t);var n=this.subarray(t,e);return Object.setPrototypeOf(n,a.prototype),n},a.prototype.readUIntLE=function(t,e,r){t>>>=0,e>>>=0,r||b(t,e,this.length);for(var n=this[t],i=1,o=0;++o<e&&(i*=256);)n+=this[t+o]*i;return n},a.prototype.readUIntBE=function(t,e,r){t>>>=0,e>>>=0,r||b(t,e,this.length);for(var n=this[t+--e],i=1;e>0&&(i*=256);)n+=this[t+--e]*i;return n},a.prototype.readUInt8=function(t,e){return t>>>=0,e||b(t,1,this.length),this[t]},a.prototype.readUInt16LE=function(t,e){return t>>>=0,e||b(t,2,this.length),this[t]|this[t+1]<<8},a.prototype.readUInt16BE=function(t,e){return t>>>=0,e||b(t,2,this.length),this[t]<<8|this[t+1]},a.prototype.readUInt32LE=function(t,e){return t>>>=0,e||b(t,4,this.length),(this[t]|this[t+1]<<8|this[t+2]<<16)+0x1000000*this[t+3]},a.prototype.readUInt32BE=function(t,e){return t>>>=0,e||b(t,4,this.length),0x1000000*this[t]+(this[t+1]<<16|this[t+2]<<8|this[t+3])},a.prototype.readIntLE=function(t,e,r){t>>>=0,e>>>=0,r||b(t,e,this.length);for(var n=this[t],i=1,o=0;++o<e&&(i*=256);)n+=this[t+o]*i;return n>=(i*=128)&&(n-=Math.pow(2,8*e)),n},a.prototype.readIntBE=function(t,e,r){t>>>=0,e>>>=0,r||b(t,e,this.length);for(var n=e,i=1,o=this[t+--n];n>0&&(i*=256);)o+=this[t+--n]*i;return o>=(i*=128)&&(o-=Math.pow(2,8*e)),o},a.prototype.readInt8=function(t,e){return(t>>>=0,e||b(t,1,this.length),128&this[t])?-((255-this[t]+1)*1):this[t]},a.prototype.readInt16LE=function(t,e){t>>>=0,e||b(t,2,this.length);var r=this[t]|this[t+1]<<8;return 32768&r?0xffff0000|r:r},a.prototype.readInt16BE=function(t,e){t>>>=0,e||b(t,2,this.length);var r=this[t+1]|this[t]<<8;return 32768&r?0xffff0000|r:r},a.prototype.readInt32LE=function(t,e){return t>>>=0,e||b(t,4,this.length),this[t]|this[t+1]<<8|this[t+2]<<16|this[t+3]<<24},a.prototype.readInt32BE=function(t,e){return t>>>=0,e||b(t,4,this.length),this[t]<<24|this[t+1]<<16|this[t+2]<<8|this[t+3]},a.prototype.readFloatLE=function(t,e){return t>>>=0,e||b(t,4,this.length),i.read(this,t,!0,23,4)},a.prototype.readFloatBE=function(t,e){return t>>>=0,e||b(t,4,this.length),i.read(this,t,!1,23,4)},a.prototype.readDoubleLE=function(t,e){return t>>>=0,e||b(t,8,this.length),i.read(this,t,!0,52,8)},a.prototype.readDoubleBE=function(t,e){return t>>>=0,e||b(t,8,this.length),i.read(this,t,!1,52,8)},a.prototype.writeUIntLE=function(t,e,r,n){if(t*=1,e>>>=0,r>>>=0,!n){var i=Math.pow(2,8*r)-1;w(this,t,e,r,i,0)}var o=1,s=0;for(this[e]=255&t;++s<r&&(o*=256);)this[e+s]=t/o&255;return e+r},a.prototype.writeUIntBE=function(t,e,r,n){if(t*=1,e>>>=0,r>>>=0,!n){var i=Math.pow(2,8*r)-1;w(this,t,e,r,i,0)}var o=r-1,s=1;for(this[e+o]=255&t;--o>=0&&(s*=256);)this[e+o]=t/s&255;return e+r},a.prototype.writeUInt8=function(t,e,r){return t*=1,e>>>=0,r||w(this,t,e,1,255,0),this[e]=255&t,e+1},a.prototype.writeUInt16LE=function(t,e,r){return t*=1,e>>>=0,r||w(this,t,e,2,65535,0),this[e]=255&t,this[e+1]=t>>>8,e+2},a.prototype.writeUInt16BE=function(t,e,r){return t*=1,e>>>=0,r||w(this,t,e,2,65535,0),this[e]=t>>>8,this[e+1]=255&t,e+2},a.prototype.writeUInt32LE=function(t,e,r){return t*=1,e>>>=0,r||w(this,t,e,4,0xffffffff,0),this[e+3]=t>>>24,this[e+2]=t>>>16,this[e+1]=t>>>8,this[e]=255&t,e+4},a.prototype.writeUInt32BE=function(t,e,r){return t*=1,e>>>=0,r||w(this,t,e,4,0xffffffff,0),this[e]=t>>>24,this[e+1]=t>>>16,this[e+2]=t>>>8,this[e+3]=255&t,e+4},a.prototype.writeIntLE=function(t,e,r,n){if(t*=1,e>>>=0,!n){var i=Math.pow(2,8*r-1);w(this,t,e,r,i-1,-i)}var o=0,s=1,a=0;for(this[e]=255&t;++o<r&&(s*=256);)t<0&&0===a&&0!==this[e+o-1]&&(a=1),this[e+o]=(t/s>>0)-a&255;return e+r},a.prototype.writeIntBE=function(t,e,r,n){if(t*=1,e>>>=0,!n){var i=Math.pow(2,8*r-1);w(this,t,e,r,i-1,-i)}var o=r-1,s=1,a=0;for(this[e+o]=255&t;--o>=0&&(s*=256);)t<0&&0===a&&0!==this[e+o+1]&&(a=1),this[e+o]=(t/s>>0)-a&255;return e+r},a.prototype.writeInt8=function(t,e,r){return t*=1,e>>>=0,r||w(this,t,e,1,127,-128),t<0&&(t=255+t+1),this[e]=255&t,e+1},a.prototype.writeInt16LE=function(t,e,r){return t*=1,e>>>=0,r||w(this,t,e,2,32767,-32768),this[e]=255&t,this[e+1]=t>>>8,e+2},a.prototype.writeInt16BE=function(t,e,r){return t*=1,e>>>=0,r||w(this,t,e,2,32767,-32768),this[e]=t>>>8,this[e+1]=255&t,e+2},a.prototype.writeInt32LE=function(t,e,r){return t*=1,e>>>=0,r||w(this,t,e,4,0x7fffffff,-0x80000000),this[e]=255&t,this[e+1]=t>>>8,this[e+2]=t>>>16,this[e+3]=t>>>24,e+4},a.prototype.writeInt32BE=function(t,e,r){return t*=1,e>>>=0,r||w(this,t,e,4,0x7fffffff,-0x80000000),t<0&&(t=0xffffffff+t+1),this[e]=t>>>24,this[e+1]=t>>>16,this[e+2]=t>>>8,this[e+3]=255&t,e+4},a.prototype.writeFloatLE=function(t,e,r){return E(this,t,e,!0,r)},a.prototype.writeFloatBE=function(t,e,r){return E(this,t,e,!1,r)},a.prototype.writeDoubleLE=function(t,e,r){return A(this,t,e,!0,r)},a.prototype.writeDoubleBE=function(t,e,r){return A(this,t,e,!1,r)},a.prototype.copy=function(t,e,r,n){if(!a.isBuffer(t))throw TypeError("argument should be a Buffer");if(r||(r=0),n||0===n||(n=this.length),e>=t.length&&(e=t.length),e||(e=0),n>0&&n<r&&(n=r),n===r||0===t.length||0===this.length)return 0;if(e<0)throw RangeError("targetStart out of bounds");if(r<0||r>=this.length)throw RangeError("Index out of range");if(n<0)throw RangeError("sourceEnd out of bounds");n>this.length&&(n=this.length),t.length-e<n-r&&(n=t.length-e+r);var i=n-r;if(this===t&&"function"==typeof Uint8Array.prototype.copyWithin)this.copyWithin(e,r,n);else if(this===t&&r<e&&e<n)for(var o=i-1;o>=0;--o)t[o+e]=this[o+r];else Uint8Array.prototype.set.call(t,this.subarray(r,n),e);return i},a.prototype.fill=function(t,e,r,n){if("string"==typeof t){if("string"==typeof e?(n=e,e=0,r=this.length):"string"==typeof r&&(n=r,r=this.length),void 0!==n&&"string"!=typeof n)throw TypeError("encoding must be a string");if("string"==typeof n&&!a.isEncoding(n))throw TypeError("Unknown encoding: "+n);if(1===t.length){var i,o=t.charCodeAt(0);("utf8"===n&&o<128||"latin1"===n)&&(t=o)}}else"number"==typeof t?t&=255:"boolean"==typeof t&&(t=Number(t));if(e<0||this.length<e||this.length<r)throw RangeError("Out of range index");if(r<=e)return this;if(e>>>=0,r=void 0===r?this.length:r>>>0,t||(t=0),"number"==typeof t)for(i=e;i<r;++i)this[i]=t;else{var s=a.isBuffer(t)?t:a.from(t,n),f=s.length;if(0===f)throw TypeError('The value "'+t+'" is invalid for argument "value"');for(i=0;i<r-e;++i)this[i+e]=s[i%f]}return this};var I=/[^+/0-9A-Za-z-_]/g;function S(t,e){e=e||1/0;for(var r,n=t.length,i=null,o=[],s=0;s<n;++s){if((r=t.charCodeAt(s))>55295&&r<57344){if(!i){if(r>56319||s+1===n){(e-=3)>-1&&o.push(239,191,189);continue}i=r;continue}if(r<56320){(e-=3)>-1&&o.push(239,191,189),i=r;continue}r=(i-55296<<10|r-56320)+65536}else i&&(e-=3)>-1&&o.push(239,191,189);if(i=null,r<128){if((e-=1)<0)break;o.push(r)}else if(r<2048){if((e-=2)<0)break;o.push(r>>6|192,63&r|128)}else if(r<65536){if((e-=3)<0)break;o.push(r>>12|224,r>>6&63|128,63&r|128)}else if(r<1114112){if((e-=4)<0)break;o.push(r>>18|240,r>>12&63|128,r>>6&63|128,63&r|128)}else throw Error("Invalid code point")}return o}function C(t){for(var e=[],r=0;r<t.length;++r)e.push(255&t.charCodeAt(r));return e}function _(t){return n.toByteArray(function(t){if((t=(t=t.split("=")[0]).trim().replace(I,"")).length<2)return"";for(;t.length%4!=0;)t+="=";return t}(t))}function T(t,e,r,n){for(var i=0;i<n&&!(i+r>=e.length)&&!(i>=t.length);++i)e[i+r]=t[i];return i}function O(t,e){return t instanceof e||null!=t&&null!=t.constructor&&null!=t.constructor.name&&t.constructor.name===e.name}var D=function(){for(var t="0123456789abcdef",e=Array(256),r=0;r<16;++r)for(var n=16*r,i=0;i<16;++i)e[n+i]=t[r]+t[i];return e}()},783:function(t,e){e.read=function(t,e,r,n,i){var o,s,a=8*i-n-1,f=(1<<a)-1,h=f>>1,l=-7,u=r?i-1:0,c=r?-1:1,p=t[e+u];for(u+=c,o=p&(1<<-l)-1,p>>=-l,l+=a;l>0;o=256*o+t[e+u],u+=c,l-=8);for(s=o&(1<<-l)-1,o>>=-l,l+=n;l>0;s=256*s+t[e+u],u+=c,l-=8);if(0===o)o=1-h;else{if(o===f)return s?NaN:1/0*(p?-1:1);s+=Math.pow(2,n),o-=h}return(p?-1:1)*s*Math.pow(2,o-n)},e.write=function(t,e,r,n,i,o){var s,a,f,h=8*o-i-1,l=(1<<h)-1,u=l>>1,c=5960464477539062e-23*(23===i),p=n?0:o-1,d=n?1:-1,g=+(e<0||0===e&&1/e<0);for(isNaN(e=Math.abs(e))||e===1/0?(a=+!!isNaN(e),s=l):(s=Math.floor(Math.log(e)/Math.LN2),e*(f=Math.pow(2,-s))<1&&(s--,f*=2),s+u>=1?e+=c/f:e+=c*Math.pow(2,1-u),e*f>=2&&(s++,f/=2),s+u>=l?(a=0,s=l):s+u>=1?(a=(e*f-1)*Math.pow(2,i),s+=u):(a=e*Math.pow(2,u-1)*Math.pow(2,i),s=0));i>=8;t[r+p]=255&a,p+=d,a/=256,i-=8);for(s=s<<i|a,h+=i;h>0;t[r+p]=255&s,p+=d,s/=256,h-=8);t[r+p-d]|=128*g}}},r={};function n(t){var i=r[t];if(void 0!==i)return i.exports;var o=r[t]={exports:{}},s=!0;try{e[t](o,o.exports,n),s=!1}finally{s&&delete r[t]}return o.exports}n.ab="//",t.exports=n(72)}()},49887:(t,e,r)=>{"use strict";r.d(e,{cY:()=>S,FA:()=>X,g:()=>W,gz:()=>ta,dM:()=>V,vA:()=>s,Hk:()=>a,K3:()=>l,u:()=>d,KA:()=>c,Uj:()=>p,p9:()=>td,gR:()=>Q,Fy:()=>T,tD:()=>tf,A4:()=>g,bD:()=>function t(e,r){if(e===r)return!0;let n=Object.keys(e),i=Object.keys(r);for(let o of n){if(!i.includes(o))return!1;let n=e[o],s=r[o];if(tn(n)&&tn(s)){if(!t(n,s))return!1}else if(n!==s)return!1}for(let t of i)if(!n.includes(t))return!1;return!0},dI:()=>tu,hp:()=>ts,T9:()=>A,Tj:()=>x,yU:()=>E,XA:()=>I,mS:()=>y,Ku:()=>tg,ZQ:()=>k,qc:()=>Z,sr:()=>L,zJ:()=>C,c1:()=>P,Im:()=>te,lT:()=>N,zW:()=>$,jZ:()=>B,$g:()=>U,lV:()=>R,nr:()=>F,Ov:()=>H,Cv:()=>Y,$L:()=>J,kH:()=>tr,gE:()=>_,Am:()=>ti,I9:()=>to,yw:()=>tt,OE:()=>tp,kj:()=>tc,As:()=>q,P1:()=>j,eX:()=>z});let n=()=>void 0;var i=r(49509);let o={NODE_CLIENT:!1,NODE_ADMIN:!1,SDK_VERSION:"${JSCORE_VERSION}"},s=function(t,e){if(!t)throw a(e)},a=function(t){return Error("Firebase Database ("+o.SDK_VERSION+") INTERNAL ASSERT FAILED: "+t)},f=function(t){let e=[],r=0;for(let n=0;n<t.length;n++){let i=t.charCodeAt(n);i<128?e[r++]=i:(i<2048?e[r++]=i>>6|192:((64512&i)==55296&&n+1<t.length&&(64512&t.charCodeAt(n+1))==56320?(i=65536+((1023&i)<<10)+(1023&t.charCodeAt(++n)),e[r++]=i>>18|240,e[r++]=i>>12&63|128):e[r++]=i>>12|224,e[r++]=i>>6&63|128),e[r++]=63&i|128)}return e},h=function(t){let e=[],r=0,n=0;for(;r<t.length;){let i=t[r++];if(i<128)e[n++]=String.fromCharCode(i);else if(i>191&&i<224){let o=t[r++];e[n++]=String.fromCharCode((31&i)<<6|63&o)}else if(i>239&&i<365){let o=t[r++],s=((7&i)<<18|(63&o)<<12|(63&t[r++])<<6|63&t[r++])-65536;e[n++]=String.fromCharCode(55296+(s>>10)),e[n++]=String.fromCharCode(56320+(1023&s))}else{let o=t[r++],s=t[r++];e[n++]=String.fromCharCode((15&i)<<12|(63&o)<<6|63&s)}}return e.join("")},l={byteToCharMap_:null,charToByteMap_:null,byteToCharMapWebSafe_:null,charToByteMapWebSafe_:null,ENCODED_VALS_BASE:"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789",get ENCODED_VALS(){return this.ENCODED_VALS_BASE+"+/="},get ENCODED_VALS_WEBSAFE(){return this.ENCODED_VALS_BASE+"-_."},HAS_NATIVE_SUPPORT:"function"==typeof atob,encodeByteArray(t,e){if(!Array.isArray(t))throw Error("encodeByteArray takes an array as a parameter");this.init_();let r=e?this.byteToCharMapWebSafe_:this.byteToCharMap_,n=[];for(let e=0;e<t.length;e+=3){let i=t[e],o=e+1<t.length,s=o?t[e+1]:0,a=e+2<t.length,f=a?t[e+2]:0,h=i>>2,l=(3&i)<<4|s>>4,u=(15&s)<<2|f>>6,c=63&f;a||(c=64,o||(u=64)),n.push(r[h],r[l],r[u],r[c])}return n.join("")},encodeString(t,e){return this.HAS_NATIVE_SUPPORT&&!e?btoa(t):this.encodeByteArray(f(t),e)},decodeString(t,e){return this.HAS_NATIVE_SUPPORT&&!e?atob(t):h(this.decodeStringToByteArray(t,e))},decodeStringToByteArray(t,e){this.init_();let r=e?this.charToByteMapWebSafe_:this.charToByteMap_,n=[];for(let e=0;e<t.length;){let i=r[t.charAt(e++)],o=e<t.length?r[t.charAt(e)]:0,s=++e<t.length?r[t.charAt(e)]:64,a=++e<t.length?r[t.charAt(e)]:64;if(++e,null==i||null==o||null==s||null==a)throw new u;let f=i<<2|o>>4;if(n.push(f),64!==s){let t=o<<4&240|s>>2;if(n.push(t),64!==a){let t=s<<6&192|a;n.push(t)}}}return n},init_(){if(!this.byteToCharMap_){this.byteToCharMap_={},this.charToByteMap_={},this.byteToCharMapWebSafe_={},this.charToByteMapWebSafe_={};for(let t=0;t<this.ENCODED_VALS.length;t++)this.byteToCharMap_[t]=this.ENCODED_VALS.charAt(t),this.charToByteMap_[this.byteToCharMap_[t]]=t,this.byteToCharMapWebSafe_[t]=this.ENCODED_VALS_WEBSAFE.charAt(t),this.charToByteMapWebSafe_[this.byteToCharMapWebSafe_[t]]=t,t>=this.ENCODED_VALS_BASE.length&&(this.charToByteMap_[this.ENCODED_VALS_WEBSAFE.charAt(t)]=t,this.charToByteMapWebSafe_[this.ENCODED_VALS.charAt(t)]=t)}}};class u extends Error{constructor(){super(...arguments),this.name="DecodeBase64StringError"}}let c=function(t){let e=f(t);return l.encodeByteArray(e,!0)},p=function(t){return c(t).replace(/\./g,"")},d=function(t){try{return l.decodeString(t,!0)}catch(t){console.error("base64Decode failed: ",t)}return null};function g(t){return function t(e,r){if(!(r instanceof Object))return r;switch(r.constructor){case Date:return new Date(r.getTime());case Object:void 0===e&&(e={});break;case Array:e=[];break;default:return r}for(let n in r)if(r.hasOwnProperty(n)&&"__proto__"!==n)e[n]=t(e[n],r[n]);return e}(void 0,t)}function y(){if("undefined"!=typeof self)return self;if("undefined"!=typeof window)return window;if(void 0!==r.g)return r.g;throw Error("Unable to locate global object.")}let m=()=>y().__FIREBASE_DEFAULTS__,v=()=>{if(void 0===i||void 0===i.env)return;let t=i.env.__FIREBASE_DEFAULTS__;if(t)return JSON.parse(t)},b=()=>{let t;if("undefined"==typeof document)return;try{t=document.cookie.match(/__FIREBASE_DEFAULTS__=([^;]+)/)}catch(t){return}let e=t&&d(t[1]);return e&&JSON.parse(e)},w=()=>{try{return n()||m()||v()||b()}catch(t){console.info(`Unable to get __FIREBASE_DEFAULTS__ due to: ${t}`);return}},x=t=>{var e,r;return null===(r=null===(e=w())||void 0===e?void 0:e.emulatorHosts)||void 0===r?void 0:r[t]},E=t=>{let e=x(t);if(!e)return;let r=e.lastIndexOf(":");if(r<=0||r+1===e.length)throw Error(`Invalid host ${e} with no separate hostname and port!`);let n=parseInt(e.substring(r+1),10);return"["===e[0]?[e.substring(1,r-1),n]:[e.substring(0,r),n]},A=()=>{var t;return null===(t=w())||void 0===t?void 0:t.config},I=t=>{var e;return null===(e=w())||void 0===e?void 0:e[`_${t}`]};class S{constructor(){this.reject=()=>{},this.resolve=()=>{},this.promise=new Promise((t,e)=>{this.resolve=t,this.reject=e})}wrapCallback(t){return(e,r)=>{e?this.reject(e):this.resolve(r),"function"==typeof t&&(this.promise.catch(()=>{}),1===t.length?t(e):t(e,r))}}}function C(t){return t.endsWith(".cloudworkstations.dev")}async function _(t){return(await fetch(t,{credentials:"include"})).ok}function T(t,e){if(t.uid)throw Error('The "uid" field is no longer supported by mockUserToken. Please use "sub" instead for Firebase Auth User ID.');let r=e||"demo-project",n=t.iat||0,i=t.sub||t.user_id;if(!i)throw Error("mockUserToken must contain 'sub' or 'user_id' field!");let o=Object.assign({iss:`https://securetoken.google.com/${r}`,aud:r,iat:n,exp:n+3600,auth_time:n,sub:i,user_id:i,firebase:{sign_in_provider:"custom",identities:{}}},t);return[p(JSON.stringify({alg:"none",type:"JWT"})),p(JSON.stringify(o)),""].join(".")}let O={},D=!1;function j(t,e){if("undefined"==typeof window||"undefined"==typeof document||!C(window.location.host)||O[t]===e||O[t]||D)return;function r(t){return`__firebase__banner__${t}`}O[t]=e;let n="__firebase__banner",i=function(){let t={prod:[],emulator:[]};for(let e of Object.keys(O))O[e]?t.emulator.push(e):t.prod.push(e);return t}().prod.length>0;function o(){let t,e;let o=(t=document.getElementById(n),e=!1,t||((t=document.createElement("div")).setAttribute("id",n),e=!0),{created:e,element:t}),s=r("text"),a=document.getElementById(s)||document.createElement("span"),f=r("learnmore"),h=document.getElementById(f)||document.createElement("a"),l=r("preprendIcon"),u=document.getElementById(l)||document.createElementNS("http://www.w3.org/2000/svg","svg");if(o.created){let t=o.element;t.style.display="flex",t.style.background="#7faaf0",t.style.position="fixed",t.style.bottom="5px",t.style.left="5px",t.style.padding=".5em",t.style.borderRadius="5px",t.style.alignItems="center",h.setAttribute("id",f),h.innerText="Learn more",h.href="https://firebase.google.com/docs/studio/preview-apps#preview-backend",h.setAttribute("target","__blank"),h.style.paddingLeft="5px",h.style.textDecoration="underline";let e=function(){let t=document.createElement("span");return t.style.cursor="pointer",t.style.marginLeft="16px",t.style.fontSize="24px",t.innerHTML=" &times;",t.onclick=()=>{D=!0,function(){let t=document.getElementById(n);t&&t.remove()}()},t}();u.setAttribute("width","24"),u.setAttribute("id",l),u.setAttribute("height","24"),u.setAttribute("viewBox","0 0 24 24"),u.setAttribute("fill","none"),u.style.marginLeft="-6px",t.append(u,a,h,e),document.body.appendChild(t)}i?(a.innerText="Preview backend disconnected.",u.innerHTML=`<g clip-path="url(#clip0_6013_33858)">
<path d="M4.8 17.6L12 5.6L19.2 17.6H4.8ZM6.91667 16.4H17.0833L12 7.93333L6.91667 16.4ZM12 15.6C12.1667 15.6 12.3056 15.5444 12.4167 15.4333C12.5389 15.3111 12.6 15.1667 12.6 15C12.6 14.8333 12.5389 14.6944 12.4167 14.5833C12.3056 14.4611 12.1667 14.4 12 14.4C11.8333 14.4 11.6889 14.4611 11.5667 14.5833C11.4556 14.6944 11.4 14.8333 11.4 15C11.4 15.1667 11.4556 15.3111 11.5667 15.4333C11.6889 15.5444 11.8333 15.6 12 15.6ZM11.4 13.6H12.6V10.4H11.4V13.6Z" fill="#212121"/>
</g>
<defs>
<clipPath id="clip0_6013_33858">
<rect width="24" height="24" fill="white"/>
</clipPath>
</defs>`):(u.innerHTML=`<g clip-path="url(#clip0_6083_34804)">
<path d="M11.4 15.2H12.6V11.2H11.4V15.2ZM12 10C12.1667 10 12.3056 9.94444 12.4167 9.83333C12.5389 9.71111 12.6 9.56667 12.6 9.4C12.6 9.23333 12.5389 9.09444 12.4167 8.98333C12.3056 8.86111 12.1667 8.8 12 8.8C11.8333 8.8 11.6889 8.86111 11.5667 8.98333C11.4556 9.09444 11.4 9.23333 11.4 9.4C11.4 9.56667 11.4556 9.71111 11.5667 9.83333C11.6889 9.94444 11.8333 10 12 10ZM12 18.4C11.1222 18.4 10.2944 18.2333 9.51667 17.9C8.73889 17.5667 8.05556 17.1111 7.46667 16.5333C6.88889 15.9444 6.43333 15.2611 6.1 14.4833C5.76667 13.7056 5.6 12.8778 5.6 12C5.6 11.1111 5.76667 10.2833 6.1 9.51667C6.43333 8.73889 6.88889 8.06111 7.46667 7.48333C8.05556 6.89444 8.73889 6.43333 9.51667 6.1C10.2944 5.76667 11.1222 5.6 12 5.6C12.8889 5.6 13.7167 5.76667 14.4833 6.1C15.2611 6.43333 15.9389 6.89444 16.5167 7.48333C17.1056 8.06111 17.5667 8.73889 17.9 9.51667C18.2333 10.2833 18.4 11.1111 18.4 12C18.4 12.8778 18.2333 13.7056 17.9 14.4833C17.5667 15.2611 17.1056 15.9444 16.5167 16.5333C15.9389 17.1111 15.2611 17.5667 14.4833 17.9C13.7167 18.2333 12.8889 18.4 12 18.4ZM12 17.2C13.4444 17.2 14.6722 16.6944 15.6833 15.6833C16.6944 14.6722 17.2 13.4444 17.2 12C17.2 10.5556 16.6944 9.32778 15.6833 8.31667C14.6722 7.30555 13.4444 6.8 12 6.8C10.5556 6.8 9.32778 7.30555 8.31667 8.31667C7.30556 9.32778 6.8 10.5556 6.8 12C6.8 13.4444 7.30556 14.6722 8.31667 15.6833C9.32778 16.6944 10.5556 17.2 12 17.2Z" fill="#212121"/>
</g>
<defs>
<clipPath id="clip0_6083_34804">
<rect width="24" height="24" fill="white"/>
</clipPath>
</defs>`,a.innerText="Preview backend running in this workspace."),a.setAttribute("id",s)}"loading"===document.readyState?window.addEventListener("DOMContentLoaded",o):o()}function k(){return"undefined"!=typeof navigator&&"string"==typeof navigator.userAgent?navigator.userAgent:""}function B(){return"undefined"!=typeof window&&!!(window.cordova||window.phonegap||window.PhoneGap)&&/ios|iphone|ipod|ipad|android|blackberry|iemobile/i.test(k())}function M(){var t;let e=null===(t=w())||void 0===t?void 0:t.forceEnvironment;if("node"===e)return!0;if("browser"===e)return!1;try{return"[object process]"===Object.prototype.toString.call(r.g.process)}catch(t){return!1}}function P(){return"undefined"!=typeof navigator&&"Cloudflare-Workers"===navigator.userAgent}function L(){let t="object"==typeof chrome?chrome.runtime:"object"==typeof browser?browser.runtime:void 0;return"object"==typeof t&&void 0!==t.id}function R(){return"object"==typeof navigator&&"ReactNative"===navigator.product}function N(){let t=k();return t.indexOf("MSIE ")>=0||t.indexOf("Trident/")>=0}function U(){return!0===o.NODE_CLIENT||!0===o.NODE_ADMIN}function F(){return!M()&&!!navigator.userAgent&&navigator.userAgent.includes("Safari")&&!navigator.userAgent.includes("Chrome")}function H(){return!M()&&!!navigator.userAgent&&(navigator.userAgent.includes("Safari")||navigator.userAgent.includes("WebKit"))&&!navigator.userAgent.includes("Chrome")}function $(){try{return"object"==typeof indexedDB}catch(t){return!1}}function z(){return new Promise((t,e)=>{try{let r=!0,n="validate-browser-context-for-indexeddb-analytics-module",i=self.indexedDB.open(n);i.onsuccess=()=>{i.result.close(),r||self.indexedDB.deleteDatabase(n),t(!0)},i.onupgradeneeded=()=>{r=!1},i.onerror=()=>{var t;e((null===(t=i.error)||void 0===t?void 0:t.message)||"")}}catch(t){e(t)}})}function V(){return"undefined"!=typeof navigator&&!!navigator.cookieEnabled}class W extends Error{constructor(t,e,r){super(e),this.code=t,this.customData=r,this.name="FirebaseError",Object.setPrototypeOf(this,W.prototype),Error.captureStackTrace&&Error.captureStackTrace(this,X.prototype.create)}}class X{constructor(t,e,r){this.service=t,this.serviceName=e,this.errors=r}create(t,...e){var r,n;let i=e[0]||{},o=`${this.service}/${t}`,s=this.errors[t],a=s?(r=s,n=i,r.replace(K,(t,e)=>{let r=n[e];return null!=r?String(r):`<${e}?>`})):"Error",f=`${this.serviceName}: ${a} (${o}).`;return new W(o,f,i)}}let K=/\{\$([^}]+)}/g;function J(t){return JSON.parse(t)}function q(t){return JSON.stringify(t)}let G=function(t){let e={},r={},n={},i="";try{let o=t.split(".");e=J(d(o[0])||""),r=J(d(o[1])||""),i=o[2],n=r.d||{},delete r.d}catch(t){}return{header:e,claims:r,data:n,signature:i}},Y=function(t){let e=G(t).claims;return!!e&&"object"==typeof e&&e.hasOwnProperty("iat")},Z=function(t){let e=G(t).claims;return"object"==typeof e&&!0===e.admin};function Q(t,e){return Object.prototype.hasOwnProperty.call(t,e)}function tt(t,e){return Object.prototype.hasOwnProperty.call(t,e)?t[e]:void 0}function te(t){for(let e in t)if(Object.prototype.hasOwnProperty.call(t,e))return!1;return!0}function tr(t,e,r){let n={};for(let i in t)Object.prototype.hasOwnProperty.call(t,i)&&(n[i]=e.call(r,t[i],i,t));return n}function tn(t){return null!==t&&"object"==typeof t}function ti(t){let e=[];for(let[r,n]of Object.entries(t))Array.isArray(n)?n.forEach(t=>{e.push(encodeURIComponent(r)+"="+encodeURIComponent(t))}):e.push(encodeURIComponent(r)+"="+encodeURIComponent(n));return e.length?"&"+e.join("&"):""}function to(t){let e={};return t.replace(/^\?/,"").split("&").forEach(t=>{if(t){let[r,n]=t.split("=");e[decodeURIComponent(r)]=decodeURIComponent(n)}}),e}function ts(t){let e=t.indexOf("?");if(!e)return"";let r=t.indexOf("#",e);return t.substring(e,r>0?r:void 0)}class ta{constructor(){this.chain_=[],this.buf_=[],this.W_=[],this.pad_=[],this.inbuf_=0,this.total_=0,this.blockSize=64,this.pad_[0]=128;for(let t=1;t<this.blockSize;++t)this.pad_[t]=0;this.reset()}reset(){this.chain_[0]=0x67452301,this.chain_[1]=0xefcdab89,this.chain_[2]=0x98badcfe,this.chain_[3]=0x10325476,this.chain_[4]=0xc3d2e1f0,this.inbuf_=0,this.total_=0}compress_(t,e){let r,n;e||(e=0);let i=this.W_;if("string"==typeof t)for(let r=0;r<16;r++)i[r]=t.charCodeAt(e)<<24|t.charCodeAt(e+1)<<16|t.charCodeAt(e+2)<<8|t.charCodeAt(e+3),e+=4;else for(let r=0;r<16;r++)i[r]=t[e]<<24|t[e+1]<<16|t[e+2]<<8|t[e+3],e+=4;for(let t=16;t<80;t++){let e=i[t-3]^i[t-8]^i[t-14]^i[t-16];i[t]=(e<<1|e>>>31)&0xffffffff}let o=this.chain_[0],s=this.chain_[1],a=this.chain_[2],f=this.chain_[3],h=this.chain_[4];for(let t=0;t<80;t++){t<40?t<20?(r=f^s&(a^f),n=0x5a827999):(r=s^a^f,n=0x6ed9eba1):t<60?(r=s&a|f&(s|a),n=0x8f1bbcdc):(r=s^a^f,n=0xca62c1d6);let e=(o<<5|o>>>27)+r+h+n+i[t]&0xffffffff;h=f,f=a,a=(s<<30|s>>>2)&0xffffffff,s=o,o=e}this.chain_[0]=this.chain_[0]+o&0xffffffff,this.chain_[1]=this.chain_[1]+s&0xffffffff,this.chain_[2]=this.chain_[2]+a&0xffffffff,this.chain_[3]=this.chain_[3]+f&0xffffffff,this.chain_[4]=this.chain_[4]+h&0xffffffff}update(t,e){if(null==t)return;void 0===e&&(e=t.length);let r=e-this.blockSize,n=0,i=this.buf_,o=this.inbuf_;for(;n<e;){if(0===o)for(;n<=r;)this.compress_(t,n),n+=this.blockSize;if("string"==typeof t){for(;n<e;)if(i[o]=t.charCodeAt(n),++o,++n,o===this.blockSize){this.compress_(i),o=0;break}}else for(;n<e;)if(i[o]=t[n],++o,++n,o===this.blockSize){this.compress_(i),o=0;break}}this.inbuf_=o,this.total_+=e}digest(){let t=[],e=8*this.total_;this.inbuf_<56?this.update(this.pad_,56-this.inbuf_):this.update(this.pad_,this.blockSize-(this.inbuf_-56));for(let t=this.blockSize-1;t>=56;t--)this.buf_[t]=255&e,e/=256;this.compress_(this.buf_);let r=0;for(let e=0;e<5;e++)for(let n=24;n>=0;n-=8)t[r]=this.chain_[e]>>n&255,++r;return t}}function tf(t,e){let r=new th(t,e);return r.subscribe.bind(r)}class th{constructor(t,e){this.observers=[],this.unsubscribes=[],this.observerCount=0,this.task=Promise.resolve(),this.finalized=!1,this.onNoObservers=e,this.task.then(()=>{t(this)}).catch(t=>{this.error(t)})}next(t){this.forEachObserver(e=>{e.next(t)})}error(t){this.forEachObserver(e=>{e.error(t)}),this.close(t)}complete(){this.forEachObserver(t=>{t.complete()}),this.close()}subscribe(t,e,r){let n;if(void 0===t&&void 0===e&&void 0===r)throw Error("Missing Observer.");void 0===(n=!function(t,e){if("object"!=typeof t||null===t)return!1;for(let r of e)if(r in t&&"function"==typeof t[r])return!0;return!1}(t,["next","error","complete"])?{next:t,error:e,complete:r}:t).next&&(n.next=tl),void 0===n.error&&(n.error=tl),void 0===n.complete&&(n.complete=tl);let i=this.unsubscribeOne.bind(this,this.observers.length);return this.finalized&&this.task.then(()=>{try{this.finalError?n.error(this.finalError):n.complete()}catch(t){}}),this.observers.push(n),i}unsubscribeOne(t){void 0!==this.observers&&void 0!==this.observers[t]&&(delete this.observers[t],this.observerCount-=1,0===this.observerCount&&void 0!==this.onNoObservers&&this.onNoObservers(this))}forEachObserver(t){if(!this.finalized)for(let e=0;e<this.observers.length;e++)this.sendOne(e,t)}sendOne(t,e){this.task.then(()=>{if(void 0!==this.observers&&void 0!==this.observers[t])try{e(this.observers[t])}catch(t){"undefined"!=typeof console&&console.error&&console.error(t)}})}close(t){!this.finalized&&(this.finalized=!0,void 0!==t&&(this.finalError=t),this.task.then(()=>{this.observers=void 0,this.onNoObservers=void 0}))}}function tl(){}function tu(t,e){return`${t} failed: ${e} argument `}let tc=function(t){let e=[],r=0;for(let n=0;n<t.length;n++){let i=t.charCodeAt(n);if(i>=55296&&i<=56319){let e=i-55296;s(++n<t.length,"Surrogate pair missing trail surrogate."),i=65536+(e<<10)+(t.charCodeAt(n)-56320)}i<128?e[r++]=i:(i<2048?e[r++]=i>>6|192:(i<65536?e[r++]=i>>12|224:(e[r++]=i>>18|240,e[r++]=i>>12&63|128),e[r++]=i>>6&63|128),e[r++]=63&i|128)}return e},tp=function(t){let e=0;for(let r=0;r<t.length;r++){let n=t.charCodeAt(r);n<128?e++:n<2048?e+=2:n>=55296&&n<=56319?(e+=4,r++):e+=3}return e};function td(t,e=1e3,r=2){let n=e*Math.pow(r,t),i=Math.round(.5*n*(Math.random()-.5)*2);return Math.min(144e5,n+i)}function tg(t){return t&&t._delegate?t._delegate:t}},56391:(t,e,r)=>{"use strict";r.d(e,{h1:()=>a,uA:()=>i});var n=r(49887);class i{constructor(t,e,r){this.name=t,this.instanceFactory=e,this.type=r,this.multipleInstances=!1,this.serviceProps={},this.instantiationMode="LAZY",this.onInstanceCreated=null}setInstantiationMode(t){return this.instantiationMode=t,this}setMultipleInstances(t){return this.multipleInstances=t,this}setServiceProps(t){return this.serviceProps=t,this}setInstanceCreatedCallback(t){return this.onInstanceCreated=t,this}}let o="[DEFAULT]";class s{constructor(t,e){this.name=t,this.container=e,this.component=null,this.instances=new Map,this.instancesDeferred=new Map,this.instancesOptions=new Map,this.onInitCallbacks=new Map}get(t){let e=this.normalizeInstanceIdentifier(t);if(!this.instancesDeferred.has(e)){let t=new n.cY;if(this.instancesDeferred.set(e,t),this.isInitialized(e)||this.shouldAutoInitialize())try{let r=this.getOrInitializeService({instanceIdentifier:e});r&&t.resolve(r)}catch(t){}}return this.instancesDeferred.get(e).promise}getImmediate(t){var e;let r=this.normalizeInstanceIdentifier(null==t?void 0:t.identifier),n=null!==(e=null==t?void 0:t.optional)&&void 0!==e&&e;if(this.isInitialized(r)||this.shouldAutoInitialize())try{return this.getOrInitializeService({instanceIdentifier:r})}catch(t){if(n)return null;throw t}else{if(n)return null;throw Error(`Service ${this.name} is not available`)}}getComponent(){return this.component}setComponent(t){if(t.name!==this.name)throw Error(`Mismatching Component ${t.name} for Provider ${this.name}.`);if(this.component)throw Error(`Component for ${this.name} has already been provided`);if(this.component=t,this.shouldAutoInitialize()){if("EAGER"===t.instantiationMode)try{this.getOrInitializeService({instanceIdentifier:o})}catch(t){}for(let[t,e]of this.instancesDeferred.entries()){let r=this.normalizeInstanceIdentifier(t);try{let t=this.getOrInitializeService({instanceIdentifier:r});e.resolve(t)}catch(t){}}}}clearInstance(t=o){this.instancesDeferred.delete(t),this.instancesOptions.delete(t),this.instances.delete(t)}async delete(){let t=Array.from(this.instances.values());await Promise.all([...t.filter(t=>"INTERNAL"in t).map(t=>t.INTERNAL.delete()),...t.filter(t=>"_delete"in t).map(t=>t._delete())])}isComponentSet(){return null!=this.component}isInitialized(t=o){return this.instances.has(t)}getOptions(t=o){return this.instancesOptions.get(t)||{}}initialize(t={}){let{options:e={}}=t,r=this.normalizeInstanceIdentifier(t.instanceIdentifier);if(this.isInitialized(r))throw Error(`${this.name}(${r}) has already been initialized`);if(!this.isComponentSet())throw Error(`Component ${this.name} has not been registered yet`);let n=this.getOrInitializeService({instanceIdentifier:r,options:e});for(let[t,e]of this.instancesDeferred.entries())r===this.normalizeInstanceIdentifier(t)&&e.resolve(n);return n}onInit(t,e){var r;let n=this.normalizeInstanceIdentifier(e),i=null!==(r=this.onInitCallbacks.get(n))&&void 0!==r?r:new Set;i.add(t),this.onInitCallbacks.set(n,i);let o=this.instances.get(n);return o&&t(o,n),()=>{i.delete(t)}}invokeOnInitCallbacks(t,e){let r=this.onInitCallbacks.get(e);if(r)for(let n of r)try{n(t,e)}catch(t){}}getOrInitializeService({instanceIdentifier:t,options:e={}}){var r;let n=this.instances.get(t);if(!n&&this.component&&(n=this.component.instanceFactory(this.container,{instanceIdentifier:(r=t)===o?void 0:r,options:e}),this.instances.set(t,n),this.instancesOptions.set(t,e),this.invokeOnInitCallbacks(n,t),this.component.onInstanceCreated))try{this.component.onInstanceCreated(this.container,t,n)}catch(t){}return n||null}normalizeInstanceIdentifier(t=o){return this.component?this.component.multipleInstances?t:o:t}shouldAutoInitialize(){return!!this.component&&"EXPLICIT"!==this.component.instantiationMode}}class a{constructor(t){this.name=t,this.providers=new Map}addComponent(t){let e=this.getProvider(t.name);if(e.isComponentSet())throw Error(`Component ${t.name} has already been registered with ${this.name}`);e.setComponent(t)}addOrOverwriteComponent(t){this.getProvider(t.name).isComponentSet()&&this.providers.delete(t.name),this.addComponent(t)}getProvider(t){if(this.providers.has(t))return this.providers.get(t);let e=new s(t,this);return this.providers.set(t,e),e}getProviders(){return Array.from(this.providers.values())}}},80927:(t,e,r)=>{"use strict";r.d(e,{Ao:()=>h,Bx:()=>o,Jh:()=>f,O4:()=>s,ZS:()=>n,fF:()=>l,iO:()=>i,ro:()=>a});var n,i,o,s,a,f,h,l,u="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{},c={};(function(){var t,e,r,p="function"==typeof Object.defineProperties?Object.defineProperty:function(t,e,r){return t==Array.prototype||t==Object.prototype||(t[e]=r.value),t},d=function(t){t=["object"==typeof globalThis&&globalThis,t,"object"==typeof window&&window,"object"==typeof self&&self,"object"==typeof u&&u];for(var e=0;e<t.length;++e){var r=t[e];if(r&&r.Math==Math)return r}throw Error("Cannot find global object")}(this);!function(t,e){if(e)t:{var r=d;t=t.split(".");for(var n=0;n<t.length-1;n++){var i=t[n];if(!(i in r))break t;r=r[i]}(e=e(n=r[t=t[t.length-1]]))!=n&&null!=e&&p(r,t,{configurable:!0,writable:!0,value:e})}}("Array.prototype.values",function(t){return t||function(){var t,e,r,n;return t=this,t instanceof String&&(t+=""),e=0,r=!1,(n={next:function(){if(!r&&e<t.length){var n,i=e++;return{value:(n=0,t[i]),done:!1}}return r=!0,{done:!0,value:void 0}}})[Symbol.iterator]=function(){return n},n}});var g=g||{},y=this||self;function m(t){var e=typeof t;return"array"==(e="object"!=e?e:t?Array.isArray(t)?"array":e:"null")||"object"==e&&"number"==typeof t.length}function v(t){var e=typeof t;return"object"==e&&null!=t||"function"==e}function b(t,e,r){return t.call.apply(t.bind,arguments)}function w(t,e,r){if(!t)throw Error();if(2<arguments.length){var n=Array.prototype.slice.call(arguments,2);return function(){var r=Array.prototype.slice.call(arguments);return Array.prototype.unshift.apply(r,n),t.apply(e,r)}}return function(){return t.apply(e,arguments)}}function x(t,e,r){return(x=Function.prototype.bind&&-1!=Function.prototype.bind.toString().indexOf("native code")?b:w).apply(null,arguments)}function E(t,e){var r=Array.prototype.slice.call(arguments,1);return function(){var e=r.slice();return e.push.apply(e,arguments),t.apply(this,e)}}function A(t,e){function r(){}r.prototype=e.prototype,t.aa=e.prototype,t.prototype=new r,t.prototype.constructor=t,t.Qb=function(t,r,n){for(var i=Array(arguments.length-2),o=2;o<arguments.length;o++)i[o-2]=arguments[o];return e.prototype[r].apply(t,i)}}function I(t){let e=t.length;if(0<e){let r=Array(e);for(let n=0;n<e;n++)r[n]=t[n];return r}return[]}function S(t,e){for(let e=1;e<arguments.length;e++){let r=arguments[e];if(m(r)){let e=t.length||0,n=r.length||0;t.length=e+n;for(let i=0;i<n;i++)t[e+i]=r[i]}else t.push(r)}}class C{constructor(t,e){this.i=t,this.j=e,this.h=0,this.g=null}get(){let t;return 0<this.h?(this.h--,t=this.g,this.g=t.next,t.next=null):t=this.i(),t}}function _(t){return/^[\s\xa0]*$/.test(t)}function T(){var t=y.navigator;return t&&(t=t.userAgent)?t:""}function O(t){return O[" "](t),t}O[" "]=function(){};var D=-1!=T().indexOf("Gecko")&&(-1==T().toLowerCase().indexOf("webkit")||-1!=T().indexOf("Edge"))&&-1==T().indexOf("Trident")&&-1==T().indexOf("MSIE")&&-1==T().indexOf("Edge");function j(t,e,r){for(let n in t)e.call(r,t[n],n,t)}function k(t){let e={};for(let r in t)e[r]=t[r];return e}let B="constructor hasOwnProperty isPrototypeOf propertyIsEnumerable toLocaleString toString valueOf".split(" ");function M(t,e){let r,n;for(let e=1;e<arguments.length;e++){for(r in n=arguments[e])t[r]=n[r];for(let e=0;e<B.length;e++)r=B[e],Object.prototype.hasOwnProperty.call(n,r)&&(t[r]=n[r])}}class P{constructor(){this.h=this.g=null}add(t,e){let r=L.get();r.set(t,e),this.h?this.h.next=r:this.g=r,this.h=r}}var L=new C(()=>new R,t=>t.reset());class R{constructor(){this.next=this.g=this.h=null}set(t,e){this.h=t,this.g=e,this.next=null}reset(){this.next=this.g=this.h=null}}let N,U=!1,F=new P,H=()=>{let t=y.Promise.resolve(void 0);N=()=>{t.then($)}};var $=()=>{let t;for(var e;t=null,F.g&&(t=F.g,F.g=F.g.next,F.g||(F.h=null),t.next=null),e=t;){try{e.h.call(e.g)}catch(t){!function(t){y.setTimeout(()=>{throw t},0)}(t)}L.j(e),100>L.h&&(L.h++,e.next=L.g,L.g=e)}U=!1};function z(){this.s=this.s,this.C=this.C}function V(t,e){this.type=t,this.g=this.target=e,this.defaultPrevented=!1}z.prototype.s=!1,z.prototype.ma=function(){this.s||(this.s=!0,this.N())},z.prototype.N=function(){if(this.C)for(;this.C.length;)this.C.shift()()},V.prototype.h=function(){this.defaultPrevented=!0};var W=function(){if(!y.addEventListener||!Object.defineProperty)return!1;var t=!1,e=Object.defineProperty({},"passive",{get:function(){t=!0}});try{let t=()=>{};y.addEventListener("test",t,e),y.removeEventListener("test",t,e)}catch(t){}return t}();function X(t,e){if(V.call(this,t?t.type:""),this.relatedTarget=this.g=this.target=null,this.button=this.screenY=this.screenX=this.clientY=this.clientX=0,this.key="",this.metaKey=this.shiftKey=this.altKey=this.ctrlKey=!1,this.state=null,this.pointerId=0,this.pointerType="",this.i=null,t){var r=this.type=t.type,n=t.changedTouches&&t.changedTouches.length?t.changedTouches[0]:null;if(this.target=t.target||t.srcElement,this.g=e,e=t.relatedTarget){if(D){t:{try{O(e.nodeName);var i=!0;break t}catch(t){}i=!1}i||(e=null)}}else"mouseover"==r?e=t.fromElement:"mouseout"==r&&(e=t.toElement);this.relatedTarget=e,n?(this.clientX=void 0!==n.clientX?n.clientX:n.pageX,this.clientY=void 0!==n.clientY?n.clientY:n.pageY,this.screenX=n.screenX||0,this.screenY=n.screenY||0):(this.clientX=void 0!==t.clientX?t.clientX:t.pageX,this.clientY=void 0!==t.clientY?t.clientY:t.pageY,this.screenX=t.screenX||0,this.screenY=t.screenY||0),this.button=t.button,this.key=t.key||"",this.ctrlKey=t.ctrlKey,this.altKey=t.altKey,this.shiftKey=t.shiftKey,this.metaKey=t.metaKey,this.pointerId=t.pointerId||0,this.pointerType="string"==typeof t.pointerType?t.pointerType:K[t.pointerType]||"",this.state=t.state,this.i=t,t.defaultPrevented&&X.aa.h.call(this)}}A(X,V);var K={2:"touch",3:"pen",4:"mouse"};X.prototype.h=function(){X.aa.h.call(this);var t=this.i;t.preventDefault?t.preventDefault():t.returnValue=!1};var J="closure_listenable_"+(1e6*Math.random()|0),q=0;function G(t,e,r,n,i){this.listener=t,this.proxy=null,this.src=e,this.type=r,this.capture=!!n,this.ha=i,this.key=++q,this.da=this.fa=!1}function Y(t){t.da=!0,t.listener=null,t.proxy=null,t.src=null,t.ha=null}function Z(t){this.src=t,this.g={},this.h=0}function Q(t,e){var r=e.type;if(r in t.g){var n,i=t.g[r],o=Array.prototype.indexOf.call(i,e,void 0);(n=0<=o)&&Array.prototype.splice.call(i,o,1),n&&(Y(e),0==t.g[r].length&&(delete t.g[r],t.h--))}}function tt(t,e,r,n){for(var i=0;i<t.length;++i){var o=t[i];if(!o.da&&o.listener==e&&!!r==o.capture&&o.ha==n)return i}return -1}Z.prototype.add=function(t,e,r,n,i){var o=t.toString();(t=this.g[o])||(t=this.g[o]=[],this.h++);var s=tt(t,e,n,i);return -1<s?(e=t[s],r||(e.fa=!1)):((e=new G(e,this.src,o,!!n,i)).fa=r,t.push(e)),e};var te="closure_lm_"+(1e6*Math.random()|0),tr={};function tn(t,e,r,n,i,o){if(!e)throw Error("Invalid event type");var s=v(i)?!!i.capture:!!i,a=ta(t);if(a||(t[te]=a=new Z(t)),(r=a.add(e,r,n,s,o)).proxy)return r;if(n=function t(e){return ts.call(t.src,t.listener,e)},r.proxy=n,n.src=t,n.listener=r,t.addEventListener)W||(i=s),void 0===i&&(i=!1),t.addEventListener(e.toString(),n,i);else if(t.attachEvent)t.attachEvent(to(e.toString()),n);else if(t.addListener&&t.removeListener)t.addListener(n);else throw Error("addEventListener and attachEvent are unavailable.");return r}function ti(t){if("number"!=typeof t&&t&&!t.da){var e=t.src;if(e&&e[J])Q(e.i,t);else{var r=t.type,n=t.proxy;e.removeEventListener?e.removeEventListener(r,n,t.capture):e.detachEvent?e.detachEvent(to(r),n):e.addListener&&e.removeListener&&e.removeListener(n),(r=ta(e))?(Q(r,t),0==r.h&&(r.src=null,e[te]=null)):Y(t)}}}function to(t){return t in tr?tr[t]:tr[t]="on"+t}function ts(t,e){if(t.da)t=!0;else{e=new X(e,this);var r=t.listener,n=t.ha||t.src;t.fa&&ti(t),t=r.call(n,e)}return t}function ta(t){return(t=t[te])instanceof Z?t:null}var tf="__closure_events_fn_"+(1e9*Math.random()>>>0);function th(t){return"function"==typeof t?t:(t[tf]||(t[tf]=function(e){return t.handleEvent(e)}),t[tf])}function tl(){z.call(this),this.i=new Z(this),this.M=this,this.F=null}function tu(t,e){var r,n=t.F;if(n)for(r=[];n;n=n.F)r.push(n);if(t=t.M,n=e.type||e,"string"==typeof e)e=new V(e,t);else if(e instanceof V)e.target=e.target||t;else{var i=e;M(e=new V(n,t),i)}if(i=!0,r)for(var o=r.length-1;0<=o;o--){var s=e.g=r[o];i=tc(s,n,!0,e)&&i}if(i=tc(s=e.g=t,n,!0,e)&&i,i=tc(s,n,!1,e)&&i,r)for(o=0;o<r.length;o++)i=tc(s=e.g=r[o],n,!1,e)&&i}function tc(t,e,r,n){if(!(e=t.i.g[String(e)]))return!0;e=e.concat();for(var i=!0,o=0;o<e.length;++o){var s=e[o];if(s&&!s.da&&s.capture==r){var a=s.listener,f=s.ha||s.src;s.fa&&Q(t.i,s),i=!1!==a.call(f,n)&&i}}return i&&!n.defaultPrevented}function tp(t,e,r){if("function"==typeof t)r&&(t=x(t,r));else if(t&&"function"==typeof t.handleEvent)t=x(t.handleEvent,t);else throw Error("Invalid listener argument");return 0x7fffffff<Number(e)?-1:y.setTimeout(t,e||0)}A(tl,z),tl.prototype[J]=!0,tl.prototype.removeEventListener=function(t,e,r,n){!function t(e,r,n,i,o){if(Array.isArray(r))for(var s=0;s<r.length;s++)t(e,r[s],n,i,o);else(i=v(i)?!!i.capture:!!i,n=th(n),e&&e[J])?(e=e.i,(r=String(r).toString())in e.g&&-1<(n=tt(s=e.g[r],n,i,o))&&(Y(s[n]),Array.prototype.splice.call(s,n,1),0==s.length&&(delete e.g[r],e.h--))):e&&(e=ta(e))&&(r=e.g[r.toString()],e=-1,r&&(e=tt(r,n,i,o)),(n=-1<e?r[e]:null)&&ti(n))}(this,t,e,r,n)},tl.prototype.N=function(){if(tl.aa.N.call(this),this.i){var t,e=this.i;for(t in e.g){for(var r=e.g[t],n=0;n<r.length;n++)Y(r[n]);delete e.g[t],e.h--}}this.F=null},tl.prototype.K=function(t,e,r,n){return this.i.add(String(t),e,!1,r,n)},tl.prototype.L=function(t,e,r,n){return this.i.add(String(t),e,!0,r,n)};class td extends z{constructor(t,e){super(),this.m=t,this.l=e,this.h=null,this.i=!1,this.g=null}j(t){this.h=arguments,this.g?this.i=!0:function t(e){e.g=tp(()=>{e.g=null,e.i&&(e.i=!1,t(e))},e.l);let r=e.h;e.h=null,e.m.apply(null,r)}(this)}N(){super.N(),this.g&&(y.clearTimeout(this.g),this.g=null,this.i=!1,this.h=null)}}function tg(t){z.call(this),this.h=t,this.g={}}A(tg,z);var ty=[];function tm(t){j(t.g,function(t,e){this.g.hasOwnProperty(e)&&ti(t)},t),t.g={}}tg.prototype.N=function(){tg.aa.N.call(this),tm(this)},tg.prototype.handleEvent=function(){throw Error("EventHandler.handleEvent not implemented")};var tv=y.JSON.stringify,tb=y.JSON.parse,tw=class{stringify(t){return y.JSON.stringify(t,void 0)}parse(t){return y.JSON.parse(t,void 0)}};function tx(){}function tE(t){return t.h||(t.h=t.i())}function tA(){}tx.prototype.h=null;var tI={OPEN:"a",kb:"b",Ja:"c",wb:"d"};function tS(){V.call(this,"d")}function tC(){V.call(this,"c")}A(tS,V),A(tC,V);var t_={},tT=null;function tO(){return tT=tT||new tl}function tD(t){V.call(this,t_.La,t)}function tj(t){let e=tO();tu(e,new tD(e))}function tk(t,e){V.call(this,t_.STAT_EVENT,t),this.stat=e}function tB(t){let e=tO();tu(e,new tk(e,t))}function tM(t,e){V.call(this,t_.Ma,t),this.size=e}function tP(t,e){if("function"!=typeof t)throw Error("Fn must not be null and must be a function");return y.setTimeout(function(){t()},e)}function tL(){this.g=!0}function tR(t,e,r,n){t.info(function(){return"XMLHTTP TEXT ("+e+"): "+function(t,e){if(!t.g)return e;if(!e)return null;try{var r=JSON.parse(e);if(r){for(t=0;t<r.length;t++)if(Array.isArray(r[t])){var n=r[t];if(!(2>n.length)){var i=n[1];if(Array.isArray(i)&&!(1>i.length)){var o=i[0];if("noop"!=o&&"stop"!=o&&"close"!=o)for(var s=1;s<i.length;s++)i[s]=""}}}}return tv(r)}catch(t){return e}}(t,r)+(n?" "+n:"")})}t_.La="serverreachability",A(tD,V),t_.STAT_EVENT="statevent",A(tk,V),t_.Ma="timingevent",A(tM,V),tL.prototype.xa=function(){this.g=!1},tL.prototype.info=function(){};var tN={NO_ERROR:0,gb:1,tb:2,sb:3,nb:4,rb:5,ub:6,Ia:7,TIMEOUT:8,xb:9},tU={lb:"complete",Hb:"success",Ja:"error",Ia:"abort",zb:"ready",Ab:"readystatechange",TIMEOUT:"timeout",vb:"incrementaldata",yb:"progress",ob:"downloadprogress",Pb:"uploadprogress"};function tF(){}function tH(t,e,r,n){this.j=t,this.i=e,this.l=r,this.R=n||1,this.U=new tg(this),this.I=45e3,this.H=null,this.o=!1,this.m=this.A=this.v=this.L=this.F=this.S=this.B=null,this.D=[],this.g=null,this.C=0,this.s=this.u=null,this.X=-1,this.J=!1,this.O=0,this.M=null,this.W=this.K=this.T=this.P=!1,this.h=new t$}function t$(){this.i=null,this.g="",this.h=!1}A(tF,tx),tF.prototype.g=function(){return new XMLHttpRequest},tF.prototype.i=function(){return{}},e=new tF;var tz={},tV={};function tW(t,e,r){t.L=1,t.v=es(ee(e)),t.m=r,t.P=!0,tX(t,null)}function tX(t,e){t.F=Date.now(),tJ(t),t.A=ee(t.v);var r=t.A,n=t.R;Array.isArray(n)||(n=[String(n)]),eb(r.i,"t",n),t.C=0,r=t.j.J,t.h=new t$,t.g=e5(t.j,r?e:null,!t.m),0<t.O&&(t.M=new td(x(t.Y,t,t.g),t.O)),e=t.U,r=t.g,n=t.ca;var i="readystatechange";Array.isArray(i)||(i&&(ty[0]=i.toString()),i=ty);for(var o=0;o<i.length;o++){var s=function t(e,r,n,i,o){if(i&&i.once)return function t(e,r,n,i,o){if(Array.isArray(r)){for(var s=0;s<r.length;s++)t(e,r[s],n,i,o);return null}return n=th(n),e&&e[J]?e.L(r,n,v(i)?!!i.capture:!!i,o):tn(e,r,n,!0,i,o)}(e,r,n,i,o);if(Array.isArray(r)){for(var s=0;s<r.length;s++)t(e,r[s],n,i,o);return null}return n=th(n),e&&e[J]?e.K(r,n,v(i)?!!i.capture:!!i,o):tn(e,r,n,!1,i,o)}(r,i[o],n||e.handleEvent,!1,e.h||e);if(!s)break;e.g[s.key]=s}e=t.H?k(t.H):{},t.m?(t.u||(t.u="POST"),e["Content-Type"]="application/x-www-form-urlencoded",t.g.ea(t.A,t.u,t.m,e)):(t.u="GET",t.g.ea(t.A,t.u,null,e)),tj(),function(t,e,r,n,i,o){t.info(function(){if(t.g){if(o)for(var s="",a=o.split("&"),f=0;f<a.length;f++){var h=a[f].split("=");if(1<h.length){var l=h[0];h=h[1];var u=l.split("_");s=2<=u.length&&"type"==u[1]?s+(l+"=")+h+"&":s+(l+"=redacted&")}}else s=null}else s=o;return"XMLHTTP REQ ("+n+") [attempt "+i+"]: "+e+"\n"+r+"\n"+s})}(t.i,t.u,t.A,t.l,t.R,t.m)}function tK(t){return!!t.g&&"GET"==t.u&&2!=t.L&&t.j.Ca}function tJ(t){t.S=Date.now()+t.I,tq(t,t.I)}function tq(t,e){if(null!=t.B)throw Error("WatchDog timer not null");t.B=tP(x(t.ba,t),e)}function tG(t){t.B&&(y.clearTimeout(t.B),t.B=null)}function tY(t){0==t.j.G||t.J||e0(t.j,t)}function tZ(t){tG(t);var e=t.M;e&&"function"==typeof e.ma&&e.ma(),t.M=null,tm(t.U),t.g&&(e=t.g,t.g=null,e.abort(),e.ma())}function tQ(t,e){try{var r=t.j;if(0!=r.G&&(r.g==t||t3(r.h,t))){if(!t.K&&t3(r.h,t)&&3==r.G){try{var n=r.Da.g.parse(e)}catch(t){n=null}if(Array.isArray(n)&&3==n.length){var i=n;if(0==i[0]){t:if(!r.u){if(r.g){if(r.g.F+3e3<t.F)eQ(r),ez(r);else break t}eG(r),tB(18)}}else r.za=i[1],0<r.za-r.T&&37500>i[2]&&r.F&&0==r.v&&!r.C&&(r.C=tP(x(r.Za,r),6e3));if(1>=t6(r.h)&&r.ca){try{r.ca()}catch(t){}r.ca=void 0}}else e2(r,11)}else if((t.K||r.g==t)&&eQ(r),!_(e))for(i=r.Da.g.parse(e),e=0;e<i.length;e++){let a=i[e];if(r.T=a[0],a=a[1],2==r.G){if("c"==a[0]){r.K=a[1],r.ia=a[2];let e=a[3];null!=e&&(r.la=e,r.j.info("VER="+r.la));let i=a[4];null!=i&&(r.Aa=i,r.j.info("SVER="+r.Aa));let f=a[5];null!=f&&"number"==typeof f&&0<f&&(r.L=n=1.5*f,r.j.info("backChannelRequestTimeoutMs_="+n)),n=r;let h=t.g;if(h){let t=h.g?h.g.getResponseHeader("X-Client-Wire-Protocol"):null;if(t){var o=n.h;o.g||-1==t.indexOf("spdy")&&-1==t.indexOf("quic")&&-1==t.indexOf("h2")||(o.j=o.l,o.g=new Set,o.h&&(t5(o,o.h),o.h=null))}if(n.D){let t=h.g?h.g.getResponseHeader("X-HTTP-Session-Id"):null;t&&(n.ya=t,eo(n.I,n.D,t))}}if(r.G=3,r.l&&r.l.ua(),r.ba&&(r.R=Date.now()-t.F,r.j.info("Handshake RTT: "+r.R+"ms")),(n=r).qa=e3(n,n.J?n.ia:null,n.W),t.K){t4(n.h,t);var s=n.L;s&&(t.I=s),t.B&&(tG(t),tJ(t)),n.g=t}else eq(n);0<r.i.length&&eW(r)}else"stop"!=a[0]&&"close"!=a[0]||e2(r,7)}else 3==r.G&&("stop"==a[0]||"close"==a[0]?"stop"==a[0]?e2(r,7):e$(r):"noop"!=a[0]&&r.l&&r.l.ta(a),r.v=0)}}tj(4)}catch(t){}}tH.prototype.ca=function(t){t=t.target;let e=this.M;e&&3==eN(t)?e.j():this.Y(t)},tH.prototype.Y=function(t){try{if(t==this.g)t:{let u=eN(this.g);var e=this.g.Ba();let c=this.g.Z();if(!(3>u)&&(3!=u||this.g&&(this.h.h||this.g.oa()||eU(this.g)))){this.J||4!=u||7==e||(8==e||0>=c?tj(3):tj(2)),tG(this);var r=this.g.Z();this.X=r;e:if(tK(this)){var n=eU(this.g);t="";var i=n.length,o=4==eN(this.g);if(!this.h.i){if("undefined"==typeof TextDecoder){tZ(this),tY(this);var s="";break e}this.h.i=new y.TextDecoder}for(e=0;e<i;e++)this.h.h=!0,t+=this.h.i.decode(n[e],{stream:!(o&&e==i-1)});n.length=0,this.h.g+=t,this.C=0,s=this.h.g}else s=this.g.oa();if(this.o=200==r,function(t,e,r,n,i,o,s){t.info(function(){return"XMLHTTP RESP ("+n+") [ attempt "+i+"]: "+e+"\n"+r+"\n"+o+" "+s})}(this.i,this.u,this.A,this.l,this.R,u,r),this.o){if(this.T&&!this.K){e:{if(this.g){var a,f=this.g;if((a=f.g?f.g.getResponseHeader("X-HTTP-Initial-Response"):null)&&!_(a)){var h=a;break e}}h=null}if(r=h)tR(this.i,this.l,r,"Initial handshake response via X-HTTP-Initial-Response"),this.K=!0,tQ(this,r);else{this.o=!1,this.s=3,tB(12),tZ(this),tY(this);break t}}if(this.P){let t;for(r=!0;!this.J&&this.C<s.length;)if((t=function(t,e){var r=t.C,n=e.indexOf("\n",r);return -1==n?tV:isNaN(r=Number(e.substring(r,n)))?tz:(n+=1)+r>e.length?tV:(e=e.slice(n,n+r),t.C=n+r,e)}(this,s))==tV){4==u&&(this.s=4,tB(14),r=!1),tR(this.i,this.l,null,"[Incomplete Response]");break}else if(t==tz){this.s=4,tB(15),tR(this.i,this.l,s,"[Invalid Chunk]"),r=!1;break}else tR(this.i,this.l,t,null),tQ(this,t);if(tK(this)&&0!=this.C&&(this.h.g=this.h.g.slice(this.C),this.C=0),4!=u||0!=s.length||this.h.h||(this.s=1,tB(16),r=!1),this.o=this.o&&r,r){if(0<s.length&&!this.W){this.W=!0;var l=this.j;l.g==this&&l.ba&&!l.M&&(l.j.info("Great, no buffering proxy detected. Bytes received: "+s.length),eY(l),l.M=!0,tB(11))}}else tR(this.i,this.l,s,"[Invalid Chunked Response]"),tZ(this),tY(this)}else tR(this.i,this.l,s,null),tQ(this,s);4==u&&tZ(this),this.o&&!this.J&&(4==u?e0(this.j,this):(this.o=!1,tJ(this)))}else(function(t){let e={};t=(t.g&&2<=eN(t)&&t.g.getAllResponseHeaders()||"").split("\r\n");for(let n=0;n<t.length;n++){if(_(t[n]))continue;var r=function(t){var e=1;t=t.split(":");let r=[];for(;0<e&&t.length;)r.push(t.shift()),e--;return t.length&&r.push(t.join(":")),r}(t[n]);let i=r[0];if("string"!=typeof(r=r[1]))continue;r=r.trim();let o=e[i]||[];e[i]=o,o.push(r)}!function(t,e){for(let r in t)e.call(void 0,t[r],r,t)}(e,function(t){return t.join(", ")})})(this.g),400==r&&0<s.indexOf("Unknown SID")?(this.s=3,tB(12)):(this.s=0,tB(13)),tZ(this),tY(this)}}}catch(t){}finally{}},tH.prototype.cancel=function(){this.J=!0,tZ(this)},tH.prototype.ba=function(){this.B=null;let t=Date.now();0<=t-this.S?(function(t,e){t.info(function(){return"TIMEOUT: "+e})}(this.i,this.A),2!=this.L&&(tj(),tB(17)),tZ(this),this.s=2,tY(this)):tq(this,this.S-t)};var t0=class{constructor(t,e){this.g=t,this.map=e}};function t1(t){this.l=t||10,t=y.PerformanceNavigationTiming?0<(t=y.performance.getEntriesByType("navigation")).length&&("hq"==t[0].nextHopProtocol||"h2"==t[0].nextHopProtocol):!!(y.chrome&&y.chrome.loadTimes&&y.chrome.loadTimes()&&y.chrome.loadTimes().wasFetchedViaSpdy),this.j=t?this.l:1,this.g=null,1<this.j&&(this.g=new Set),this.h=null,this.i=[]}function t2(t){return!!t.h||!!t.g&&t.g.size>=t.j}function t6(t){return t.h?1:t.g?t.g.size:0}function t3(t,e){return t.h?t.h==e:!!t.g&&t.g.has(e)}function t5(t,e){t.g?t.g.add(e):t.h=e}function t4(t,e){t.h&&t.h==e?t.h=null:t.g&&t.g.has(e)&&t.g.delete(e)}function t8(t){if(null!=t.h)return t.i.concat(t.h.D);if(null!=t.g&&0!==t.g.size){let e=t.i;for(let r of t.g.values())e=e.concat(r.D);return e}return I(t.i)}function t7(t,e){if(t.forEach&&"function"==typeof t.forEach)t.forEach(e,void 0);else if(m(t)||"string"==typeof t)Array.prototype.forEach.call(t,e,void 0);else for(var r=function(t){if(t.na&&"function"==typeof t.na)return t.na();if(!t.V||"function"!=typeof t.V){if("undefined"!=typeof Map&&t instanceof Map)return Array.from(t.keys());if(!("undefined"!=typeof Set&&t instanceof Set)){if(m(t)||"string"==typeof t){var e=[];t=t.length;for(var r=0;r<t;r++)e.push(r);return e}for(let n in e=[],r=0,t)e[r++]=n;return e}}}(t),n=function(t){if(t.V&&"function"==typeof t.V)return t.V();if("undefined"!=typeof Map&&t instanceof Map||"undefined"!=typeof Set&&t instanceof Set)return Array.from(t.values());if("string"==typeof t)return t.split("");if(m(t)){for(var e=[],r=t.length,n=0;n<r;n++)e.push(t[n]);return e}for(n in e=[],r=0,t)e[r++]=t[n];return e}(t),i=n.length,o=0;o<i;o++)e.call(void 0,n[o],r&&r[o],t)}t1.prototype.cancel=function(){if(this.i=t8(this),this.h)this.h.cancel(),this.h=null;else if(this.g&&0!==this.g.size){for(let t of this.g.values())t.cancel();this.g.clear()}};var t9=RegExp("^(?:([^:/?#.]+):)?(?://(?:([^\\\\/?#]*)@)?([^\\\\/?#]*?)(?::([0-9]+))?(?=[\\\\/?#]|$))?([^?#]+)?(?:\\?([^#]*))?(?:#([\\s\\S]*))?$");function et(t){if(this.g=this.o=this.j="",this.s=null,this.m=this.l="",this.h=!1,t instanceof et){this.h=t.h,er(this,t.j),this.o=t.o,this.g=t.g,en(this,t.s),this.l=t.l;var e=t.i,r=new eg;r.i=e.i,e.g&&(r.g=new Map(e.g),r.h=e.h),ei(this,r),this.m=t.m}else t&&(e=String(t).match(t9))?(this.h=!1,er(this,e[1]||"",!0),this.o=ea(e[2]||""),this.g=ea(e[3]||"",!0),en(this,e[4]),this.l=ea(e[5]||"",!0),ei(this,e[6]||"",!0),this.m=ea(e[7]||"")):(this.h=!1,this.i=new eg(null,this.h))}function ee(t){return new et(t)}function er(t,e,r){t.j=r?ea(e,!0):e,t.j&&(t.j=t.j.replace(/:$/,""))}function en(t,e){if(e){if(isNaN(e=Number(e))||0>e)throw Error("Bad port number "+e);t.s=e}else t.s=null}function ei(t,e,r){var n,i;e instanceof eg?(t.i=e,n=t.i,(i=t.h)&&!n.j&&(ey(n),n.i=null,n.g.forEach(function(t,e){var r=e.toLowerCase();e!=r&&(em(this,e),eb(this,r,t))},n)),n.j=i):(r||(e=ef(e,ep)),t.i=new eg(e,t.h))}function eo(t,e,r){t.i.set(e,r)}function es(t){return eo(t,"zx",Math.floor(0x80000000*Math.random()).toString(36)+Math.abs(Math.floor(0x80000000*Math.random())^Date.now()).toString(36)),t}function ea(t,e){return t?e?decodeURI(t.replace(/%25/g,"%2525")):decodeURIComponent(t):""}function ef(t,e,r){return"string"==typeof t?(t=encodeURI(t).replace(e,eh),r&&(t=t.replace(/%25([0-9a-fA-F]{2})/g,"%$1")),t):null}function eh(t){return"%"+((t=t.charCodeAt(0))>>4&15).toString(16)+(15&t).toString(16)}et.prototype.toString=function(){var t=[],e=this.j;e&&t.push(ef(e,el,!0),":");var r=this.g;return(r||"file"==e)&&(t.push("//"),(e=this.o)&&t.push(ef(e,el,!0),"@"),t.push(encodeURIComponent(String(r)).replace(/%25([0-9a-fA-F]{2})/g,"%$1")),null!=(r=this.s)&&t.push(":",String(r))),(r=this.l)&&(this.g&&"/"!=r.charAt(0)&&t.push("/"),t.push(ef(r,"/"==r.charAt(0)?ec:eu,!0))),(r=this.i.toString())&&t.push("?",r),(r=this.m)&&t.push("#",ef(r,ed)),t.join("")};var el=/[#\/\?@]/g,eu=/[#\?:]/g,ec=/[#\?]/g,ep=/[#\?@]/g,ed=/#/g;function eg(t,e){this.h=this.g=null,this.i=t||null,this.j=!!e}function ey(t){t.g||(t.g=new Map,t.h=0,t.i&&function(t,e){if(t){t=t.split("&");for(var r=0;r<t.length;r++){var n=t[r].indexOf("="),i=null;if(0<=n){var o=t[r].substring(0,n);i=t[r].substring(n+1)}else o=t[r];e(o,i?decodeURIComponent(i.replace(/\+/g," ")):"")}}}(t.i,function(e,r){t.add(decodeURIComponent(e.replace(/\+/g," ")),r)}))}function em(t,e){ey(t),e=ew(t,e),t.g.has(e)&&(t.i=null,t.h-=t.g.get(e).length,t.g.delete(e))}function ev(t,e){return ey(t),e=ew(t,e),t.g.has(e)}function eb(t,e,r){em(t,e),0<r.length&&(t.i=null,t.g.set(ew(t,e),I(r)),t.h+=r.length)}function ew(t,e){return e=String(e),t.j&&(e=e.toLowerCase()),e}function ex(t,e,r,n,i){try{i&&(i.onload=null,i.onerror=null,i.onabort=null,i.ontimeout=null),n(r)}catch(t){}}function eE(){this.g=new tw}function eA(t){this.l=t.Ub||null,this.j=t.eb||!1}function eI(t,e){tl.call(this),this.D=t,this.o=e,this.m=void 0,this.status=this.readyState=0,this.responseType=this.responseText=this.response=this.statusText="",this.onreadystatechange=null,this.u=new Headers,this.h=null,this.B="GET",this.A="",this.g=!1,this.v=this.j=this.l=null}function eS(t){t.j.read().then(t.Pa.bind(t)).catch(t.ga.bind(t))}function eC(t){t.readyState=4,t.l=null,t.j=null,t.v=null,e_(t)}function e_(t){t.onreadystatechange&&t.onreadystatechange.call(t)}function eT(t){let e="";return j(t,function(t,r){e+=r,e+=":",e+=t,e+="\r\n"}),e}function eO(t,e,r){t:{for(n in r){var n=!1;break t}n=!0}n||(r=eT(r),"string"==typeof t?null!=r&&encodeURIComponent(String(r)):eo(t,e,r))}function eD(t){tl.call(this),this.headers=new Map,this.o=t||null,this.h=!1,this.v=this.g=null,this.D="",this.m=0,this.l="",this.j=this.B=this.u=this.A=!1,this.I=null,this.H="",this.J=!1}(r=eg.prototype).add=function(t,e){ey(this),this.i=null,t=ew(this,t);var r=this.g.get(t);return r||this.g.set(t,r=[]),r.push(e),this.h+=1,this},r.forEach=function(t,e){ey(this),this.g.forEach(function(r,n){r.forEach(function(r){t.call(e,r,n,this)},this)},this)},r.na=function(){ey(this);let t=Array.from(this.g.values()),e=Array.from(this.g.keys()),r=[];for(let n=0;n<e.length;n++){let i=t[n];for(let t=0;t<i.length;t++)r.push(e[n])}return r},r.V=function(t){ey(this);let e=[];if("string"==typeof t)ev(this,t)&&(e=e.concat(this.g.get(ew(this,t))));else{t=Array.from(this.g.values());for(let r=0;r<t.length;r++)e=e.concat(t[r])}return e},r.set=function(t,e){return ey(this),this.i=null,ev(this,t=ew(this,t))&&(this.h-=this.g.get(t).length),this.g.set(t,[e]),this.h+=1,this},r.get=function(t,e){return t&&0<(t=this.V(t)).length?String(t[0]):e},r.toString=function(){if(this.i)return this.i;if(!this.g)return"";let t=[],e=Array.from(this.g.keys());for(var r=0;r<e.length;r++){var n=e[r];let o=encodeURIComponent(String(n)),s=this.V(n);for(n=0;n<s.length;n++){var i=o;""!==s[n]&&(i+="="+encodeURIComponent(String(s[n]))),t.push(i)}}return this.i=t.join("&")},A(eA,tx),eA.prototype.g=function(){return new eI(this.l,this.j)},eA.prototype.i=(t={},function(){return t}),A(eI,tl),(r=eI.prototype).open=function(t,e){if(0!=this.readyState)throw this.abort(),Error("Error reopening a connection");this.B=t,this.A=e,this.readyState=1,e_(this)},r.send=function(t){if(1!=this.readyState)throw this.abort(),Error("need to call open() first. ");this.g=!0;let e={headers:this.u,method:this.B,credentials:this.m,cache:void 0};t&&(e.body=t),(this.D||y).fetch(new Request(this.A,e)).then(this.Sa.bind(this),this.ga.bind(this))},r.abort=function(){this.response=this.responseText="",this.u=new Headers,this.status=0,this.j&&this.j.cancel("Request was aborted.").catch(()=>{}),1<=this.readyState&&this.g&&4!=this.readyState&&(this.g=!1,eC(this)),this.readyState=0},r.Sa=function(t){if(this.g&&(this.l=t,this.h||(this.status=this.l.status,this.statusText=this.l.statusText,this.h=t.headers,this.readyState=2,e_(this)),this.g&&(this.readyState=3,e_(this),this.g))){if("arraybuffer"===this.responseType)t.arrayBuffer().then(this.Qa.bind(this),this.ga.bind(this));else if(void 0!==y.ReadableStream&&"body"in t){if(this.j=t.body.getReader(),this.o){if(this.responseType)throw Error('responseType must be empty for "streamBinaryChunks" mode responses.');this.response=[]}else this.response=this.responseText="",this.v=new TextDecoder;eS(this)}else t.text().then(this.Ra.bind(this),this.ga.bind(this))}},r.Pa=function(t){if(this.g){if(this.o&&t.value)this.response.push(t.value);else if(!this.o){var e=t.value?t.value:new Uint8Array(0);(e=this.v.decode(e,{stream:!t.done}))&&(this.response=this.responseText+=e)}t.done?eC(this):e_(this),3==this.readyState&&eS(this)}},r.Ra=function(t){this.g&&(this.response=this.responseText=t,eC(this))},r.Qa=function(t){this.g&&(this.response=t,eC(this))},r.ga=function(){this.g&&eC(this)},r.setRequestHeader=function(t,e){this.u.append(t,e)},r.getResponseHeader=function(t){return this.h&&this.h.get(t.toLowerCase())||""},r.getAllResponseHeaders=function(){if(!this.h)return"";let t=[],e=this.h.entries();for(var r=e.next();!r.done;)t.push((r=r.value)[0]+": "+r[1]),r=e.next();return t.join("\r\n")},Object.defineProperty(eI.prototype,"withCredentials",{get:function(){return"include"===this.m},set:function(t){this.m=t?"include":"same-origin"}}),A(eD,tl);var ej=/^https?$/i,ek=["POST","PUT"];function eB(t,e){t.h=!1,t.g&&(t.j=!0,t.g.abort(),t.j=!1),t.l=e,t.m=5,eM(t),eL(t)}function eM(t){t.A||(t.A=!0,tu(t,"complete"),tu(t,"error"))}function eP(t){if(t.h&&void 0!==g&&(!t.v[1]||4!=eN(t)||2!=t.Z())){if(t.u&&4==eN(t))tp(t.Ea,0,t);else if(tu(t,"readystatechange"),4==eN(t)){t.h=!1;try{let s=t.Z();switch(s){case 200:case 201:case 202:case 204:case 206:case 304:case 1223:var e,r,n=!0;break;default:n=!1}if(!(e=n)){if(r=0===s){var i=String(t.D).match(t9)[1]||null;!i&&y.self&&y.self.location&&(i=y.self.location.protocol.slice(0,-1)),r=!ej.test(i?i.toLowerCase():"")}e=r}if(e)tu(t,"complete"),tu(t,"success");else{t.m=6;try{var o=2<eN(t)?t.g.statusText:""}catch(t){o=""}t.l=o+" ["+t.Z()+"]",eM(t)}}finally{eL(t)}}}}function eL(t,e){if(t.g){eR(t);let r=t.g,n=t.v[0]?()=>{}:null;t.g=null,t.v=null,e||tu(t,"ready");try{r.onreadystatechange=n}catch(t){}}}function eR(t){t.I&&(y.clearTimeout(t.I),t.I=null)}function eN(t){return t.g?t.g.readyState:0}function eU(t){try{if(!t.g)return null;if("response"in t.g)return t.g.response;switch(t.H){case"":case"text":return t.g.responseText;case"arraybuffer":if("mozResponseArrayBuffer"in t.g)return t.g.mozResponseArrayBuffer}return null}catch(t){return null}}function eF(t,e,r){return r&&r.internalChannelParams&&r.internalChannelParams[t]||e}function eH(t){this.Aa=0,this.i=[],this.j=new tL,this.ia=this.qa=this.I=this.W=this.g=this.ya=this.D=this.H=this.m=this.S=this.o=null,this.Ya=this.U=0,this.Va=eF("failFast",!1,t),this.F=this.C=this.u=this.s=this.l=null,this.X=!0,this.za=this.T=-1,this.Y=this.v=this.B=0,this.Ta=eF("baseRetryDelayMs",5e3,t),this.cb=eF("retryDelaySeedMs",1e4,t),this.Wa=eF("forwardChannelMaxRetries",2,t),this.wa=eF("forwardChannelRequestTimeoutMs",2e4,t),this.pa=t&&t.xmlHttpFactory||void 0,this.Xa=t&&t.Tb||void 0,this.Ca=t&&t.useFetchStreams||!1,this.L=void 0,this.J=t&&t.supportsCrossDomainXhr||!1,this.K="",this.h=new t1(t&&t.concurrentRequestLimit),this.Da=new eE,this.P=t&&t.fastHandshake||!1,this.O=t&&t.encodeInitMessageHeaders||!1,this.P&&this.O&&(this.O=!1),this.Ua=t&&t.Rb||!1,t&&t.xa&&this.j.xa(),t&&t.forceLongPolling&&(this.X=!1),this.ba=!this.P&&this.X&&t&&t.detectBufferingProxy||!1,this.ja=void 0,t&&t.longPollingTimeout&&0<t.longPollingTimeout&&(this.ja=t.longPollingTimeout),this.ca=void 0,this.R=0,this.M=!1,this.ka=this.A=null}function e$(t){if(eV(t),3==t.G){var e=t.U++,r=ee(t.I);if(eo(r,"SID",t.K),eo(r,"RID",e),eo(r,"TYPE","terminate"),eK(t,r),(e=new tH(t,t.j,e)).L=2,e.v=es(ee(r)),r=!1,y.navigator&&y.navigator.sendBeacon)try{r=y.navigator.sendBeacon(e.v.toString(),"")}catch(t){}!r&&y.Image&&((new Image).src=e.v,r=!0),r||(e.g=e5(e.j,null),e.g.ea(e.v)),e.F=Date.now(),tJ(e)}e6(t)}function ez(t){t.g&&(eY(t),t.g.cancel(),t.g=null)}function eV(t){ez(t),t.u&&(y.clearTimeout(t.u),t.u=null),eQ(t),t.h.cancel(),t.s&&("number"==typeof t.s&&y.clearTimeout(t.s),t.s=null)}function eW(t){if(!t2(t.h)&&!t.s){t.s=!0;var e=t.Ga;N||H(),U||(N(),U=!0),F.add(e,t),t.B=0}}function eX(t,e){var r;r=e?e.l:t.U++;let n=ee(t.I);eo(n,"SID",t.K),eo(n,"RID",r),eo(n,"AID",t.T),eK(t,n),t.m&&t.o&&eO(n,t.m,t.o),r=new tH(t,t.j,r,t.B+1),null===t.m&&(r.H=t.o),e&&(t.i=e.D.concat(t.i)),e=eJ(t,r,1e3),r.I=Math.round(.5*t.wa)+Math.round(.5*t.wa*Math.random()),t5(t.h,r),tW(r,n,e)}function eK(t,e){t.H&&j(t.H,function(t,r){eo(e,r,t)}),t.l&&t7({},function(t,r){eo(e,r,t)})}function eJ(t,e,r){r=Math.min(t.i.length,r);var n=t.l?x(t.l.Na,t.l,t):null;t:{var i=t.i;let e=-1;for(;;){let t=["count="+r];-1==e?0<r?(e=i[0].g,t.push("ofs="+e)):e=0:t.push("ofs="+e);let o=!0;for(let s=0;s<r;s++){let r=i[s].g,a=i[s].map;if(0>(r-=e))e=Math.max(0,i[s].g-100),o=!1;else try{!function(t,e,r){let n=r||"";try{t7(t,function(t,r){let i=t;v(t)&&(i=tv(t)),e.push(n+r+"="+encodeURIComponent(i))})}catch(t){throw e.push(n+"type="+encodeURIComponent("_badmap")),t}}(a,t,"req"+r+"_")}catch(t){n&&n(a)}}if(o){n=t.join("&");break t}}}return e.D=t=t.i.splice(0,r),n}function eq(t){if(!t.g&&!t.u){t.Y=1;var e=t.Fa;N||H(),U||(N(),U=!0),F.add(e,t),t.v=0}}function eG(t){return!t.g&&!t.u&&!(3<=t.v)&&(t.Y++,t.u=tP(x(t.Fa,t),e1(t,t.v)),t.v++,!0)}function eY(t){null!=t.A&&(y.clearTimeout(t.A),t.A=null)}function eZ(t){t.g=new tH(t,t.j,"rpc",t.Y),null===t.m&&(t.g.H=t.o),t.g.O=0;var e=ee(t.qa);eo(e,"RID","rpc"),eo(e,"SID",t.K),eo(e,"AID",t.T),eo(e,"CI",t.F?"0":"1"),!t.F&&t.ja&&eo(e,"TO",t.ja),eo(e,"TYPE","xmlhttp"),eK(t,e),t.m&&t.o&&eO(e,t.m,t.o),t.L&&(t.g.I=t.L);var r=t.g;t=t.ia,r.L=1,r.v=es(ee(e)),r.m=null,r.P=!0,tX(r,t)}function eQ(t){null!=t.C&&(y.clearTimeout(t.C),t.C=null)}function e0(t,e){var r=null;if(t.g==e){eQ(t),eY(t),t.g=null;var n=2}else{if(!t3(t.h,e))return;r=e.D,t4(t.h,e),n=1}if(0!=t.G){if(e.o){if(1==n){r=e.m?e.m.length:0,e=Date.now()-e.F;var i,o=t.B;tu(n=tO(),new tM(n,r)),eW(t)}else eq(t)}else if(3==(o=e.s)||0==o&&0<e.X||!(1==n&&(i=e,!(t6(t.h)>=t.h.j-+!!t.s)&&(t.s?(t.i=i.D.concat(t.i),!0):1!=t.G&&2!=t.G&&!(t.B>=(t.Va?0:t.Wa))&&(t.s=tP(x(t.Ga,t,i),e1(t,t.B)),t.B++,!0)))||2==n&&eG(t)))switch(r&&0<r.length&&((e=t.h).i=e.i.concat(r)),o){case 1:e2(t,5);break;case 4:e2(t,10);break;case 3:e2(t,6);break;default:e2(t,2)}}}function e1(t,e){let r=t.Ta+Math.floor(Math.random()*t.cb);return t.isActive()||(r*=2),r*e}function e2(t,e){if(t.j.info("Error code "+e),2==e){var r=x(t.fb,t),n=t.Xa;let e=!n;n=new et(n||"//www.google.com/images/cleardot.gif"),y.location&&"http"==y.location.protocol||er(n,"https"),es(n),e?function(t,e){let r=new tL;if(y.Image){let n=new Image;n.onload=E(ex,r,"TestLoadImage: loaded",!0,e,n),n.onerror=E(ex,r,"TestLoadImage: error",!1,e,n),n.onabort=E(ex,r,"TestLoadImage: abort",!1,e,n),n.ontimeout=E(ex,r,"TestLoadImage: timeout",!1,e,n),y.setTimeout(function(){n.ontimeout&&n.ontimeout()},1e4),n.src=t}else e(!1)}(n.toString(),r):function(t,e){let r=new tL,n=new AbortController,i=setTimeout(()=>{n.abort(),ex(r,"TestPingServer: timeout",!1,e)},1e4);fetch(t,{signal:n.signal}).then(t=>{clearTimeout(i),t.ok?ex(r,"TestPingServer: ok",!0,e):ex(r,"TestPingServer: server error",!1,e)}).catch(()=>{clearTimeout(i),ex(r,"TestPingServer: error",!1,e)})}(n.toString(),r)}else tB(2);t.G=0,t.l&&t.l.sa(e),e6(t),eV(t)}function e6(t){if(t.G=0,t.ka=[],t.l){let e=t8(t.h);(0!=e.length||0!=t.i.length)&&(S(t.ka,e),S(t.ka,t.i),t.h.i.length=0,I(t.i),t.i.length=0),t.l.ra()}}function e3(t,e,r){var n=r instanceof et?ee(r):new et(r);if(""!=n.g)e&&(n.g=e+"."+n.g),en(n,n.s);else{var i=y.location;n=i.protocol,e=e?e+"."+i.hostname:i.hostname,i=+i.port;var o=new et(null);n&&er(o,n),e&&(o.g=e),i&&en(o,i),r&&(o.l=r),n=o}return r=t.D,e=t.ya,r&&e&&eo(n,r,e),eo(n,"VER",t.la),eK(t,n),n}function e5(t,e,r){if(e&&!t.J)throw Error("Can't create secondary domain capable XhrIo object.");return(e=new eD(t.Ca&&!t.pa?new eA({eb:r}):t.pa)).Ha(t.J),e}function e4(){}function e8(){}function e7(t,e){tl.call(this),this.g=new eH(e),this.l=t,this.h=e&&e.messageUrlParams||null,t=e&&e.messageHeaders||null,e&&e.clientProtocolHeaderRequired&&(t?t["X-Client-Protocol"]="webchannel":t={"X-Client-Protocol":"webchannel"}),this.g.o=t,t=e&&e.initMessageHeaders||null,e&&e.messageContentType&&(t?t["X-WebChannel-Content-Type"]=e.messageContentType:t={"X-WebChannel-Content-Type":e.messageContentType}),e&&e.va&&(t?t["X-WebChannel-Client-Profile"]=e.va:t={"X-WebChannel-Client-Profile":e.va}),this.g.S=t,(t=e&&e.Sb)&&!_(t)&&(this.g.m=t),this.v=e&&e.supportsCrossDomainXhr||!1,this.u=e&&e.sendRawJson||!1,(e=e&&e.httpSessionIdParam)&&!_(e)&&(this.g.D=e,null!==(t=this.h)&&e in t&&e in(t=this.h)&&delete t[e]),this.j=new re(this)}function e9(t){tS.call(this),t.__headers__&&(this.headers=t.__headers__,this.statusCode=t.__status__,delete t.__headers__,delete t.__status__);var e=t.__sm__;if(e){t:{for(let r in e){t=r;break t}t=void 0}(this.i=t)&&(t=this.i,e=null!==e&&t in e?e[t]:void 0),this.data=e}else this.data=t}function rt(){tC.call(this),this.status=1}function re(t){this.g=t}(r=eD.prototype).Ha=function(t){this.J=t},r.ea=function(t,r,n,i){if(this.g)throw Error("[goog.net.XhrIo] Object is active with another request="+this.D+"; newUri="+t);r=r?r.toUpperCase():"GET",this.D=t,this.l="",this.m=0,this.A=!1,this.h=!0,this.g=this.o?this.o.g():e.g(),this.v=this.o?tE(this.o):tE(e),this.g.onreadystatechange=x(this.Ea,this);try{this.B=!0,this.g.open(r,String(t),!0),this.B=!1}catch(t){eB(this,t);return}if(t=n||"",n=new Map(this.headers),i){if(Object.getPrototypeOf(i)===Object.prototype)for(var o in i)n.set(o,i[o]);else if("function"==typeof i.keys&&"function"==typeof i.get)for(let t of i.keys())n.set(t,i.get(t));else throw Error("Unknown input type for opt_headers: "+String(i))}for(let[e,s]of(i=Array.from(n.keys()).find(t=>"content-type"==t.toLowerCase()),o=y.FormData&&t instanceof y.FormData,!(0<=Array.prototype.indexOf.call(ek,r,void 0))||i||o||n.set("Content-Type","application/x-www-form-urlencoded;charset=utf-8"),n))this.g.setRequestHeader(e,s);this.H&&(this.g.responseType=this.H),"withCredentials"in this.g&&this.g.withCredentials!==this.J&&(this.g.withCredentials=this.J);try{eR(this),this.u=!0,this.g.send(t),this.u=!1}catch(t){eB(this,t)}},r.abort=function(t){this.g&&this.h&&(this.h=!1,this.j=!0,this.g.abort(),this.j=!1,this.m=t||7,tu(this,"complete"),tu(this,"abort"),eL(this))},r.N=function(){this.g&&(this.h&&(this.h=!1,this.j=!0,this.g.abort(),this.j=!1),eL(this,!0)),eD.aa.N.call(this)},r.Ea=function(){this.s||(this.B||this.u||this.j?eP(this):this.bb())},r.bb=function(){eP(this)},r.isActive=function(){return!!this.g},r.Z=function(){try{return 2<eN(this)?this.g.status:-1}catch(t){return -1}},r.oa=function(){try{return this.g?this.g.responseText:""}catch(t){return""}},r.Oa=function(t){if(this.g){var e=this.g.responseText;return t&&0==e.indexOf(t)&&(e=e.substring(t.length)),tb(e)}},r.Ba=function(){return this.m},r.Ka=function(){return"string"==typeof this.l?this.l:String(this.l)},(r=eH.prototype).la=8,r.G=1,r.connect=function(t,e,r,n){tB(0),this.W=t,this.H=e||{},r&&void 0!==n&&(this.H.OSID=r,this.H.OAID=n),this.F=this.X,this.I=e3(this,null,this.W),eW(this)},r.Ga=function(t){if(this.s){if(this.s=null,1==this.G){if(!t){this.U=Math.floor(1e5*Math.random()),t=this.U++;let i=new tH(this,this.j,t),o=this.o;if(this.S&&(o?M(o=k(o),this.S):o=this.S),null!==this.m||this.O||(i.H=o,o=null),this.P)t:{for(var e=0,r=0;r<this.i.length;r++){e:{var n=this.i[r];if("__data__"in n.map&&"string"==typeof(n=n.map.__data__)){n=n.length;break e}n=void 0}if(void 0===n)break;if(4096<(e+=n)){e=r;break t}if(4096===e||r===this.i.length-1){e=r+1;break t}}e=1e3}else e=1e3;e=eJ(this,i,e),eo(r=ee(this.I),"RID",t),eo(r,"CVER",22),this.D&&eo(r,"X-HTTP-Session-Id",this.D),eK(this,r),o&&(this.O?e="headers="+encodeURIComponent(String(eT(o)))+"&"+e:this.m&&eO(r,this.m,o)),t5(this.h,i),this.Ua&&eo(r,"TYPE","init"),this.P?(eo(r,"$req",e),eo(r,"SID","null"),i.T=!0,tW(i,r,null)):tW(i,r,e),this.G=2}}else 3==this.G&&(t?eX(this,t):0==this.i.length||t2(this.h)||eX(this))}},r.Fa=function(){if(this.u=null,eZ(this),this.ba&&!(this.M||null==this.g||0>=this.R)){var t=2*this.R;this.j.info("BP detection timer enabled: "+t),this.A=tP(x(this.ab,this),t)}},r.ab=function(){this.A&&(this.A=null,this.j.info("BP detection timeout reached."),this.j.info("Buffering proxy detected and switch to long-polling!"),this.F=!1,this.M=!0,tB(10),ez(this),eZ(this))},r.Za=function(){null!=this.C&&(this.C=null,ez(this),eG(this),tB(19))},r.fb=function(t){t?(this.j.info("Successfully pinged google.com"),tB(2)):(this.j.info("Failed to ping google.com"),tB(1))},r.isActive=function(){return!!this.l&&this.l.isActive(this)},(r=e4.prototype).ua=function(){},r.ta=function(){},r.sa=function(){},r.ra=function(){},r.isActive=function(){return!0},r.Na=function(){},e8.prototype.g=function(t,e){return new e7(t,e)},A(e7,tl),e7.prototype.m=function(){this.g.l=this.j,this.v&&(this.g.J=!0),this.g.connect(this.l,this.h||void 0)},e7.prototype.close=function(){e$(this.g)},e7.prototype.o=function(t){var e=this.g;if("string"==typeof t){var r={};r.__data__=t,t=r}else this.u&&((r={}).__data__=tv(t),t=r);e.i.push(new t0(e.Ya++,t)),3==e.G&&eW(e)},e7.prototype.N=function(){this.g.l=null,delete this.j,e$(this.g),delete this.g,e7.aa.N.call(this)},A(e9,tS),A(rt,tC),A(re,e4),re.prototype.ua=function(){tu(this.g,"a")},re.prototype.ta=function(t){tu(this.g,new e9(t))},re.prototype.sa=function(t){tu(this.g,new rt)},re.prototype.ra=function(){tu(this.g,"b")},e8.prototype.createWebChannel=e8.prototype.g,e7.prototype.send=e7.prototype.o,e7.prototype.open=e7.prototype.m,e7.prototype.close=e7.prototype.close,l=c.createWebChannelTransport=function(){return new e8},h=c.getStatEventTarget=function(){return tO()},f=c.Event=t_,a=c.Stat={mb:0,pb:1,qb:2,Jb:3,Ob:4,Lb:5,Mb:6,Kb:7,Ib:8,Nb:9,PROXY:10,NOPROXY:11,Gb:12,Cb:13,Db:14,Bb:15,Eb:16,Fb:17,ib:18,hb:19,jb:20},tN.NO_ERROR=0,tN.TIMEOUT=8,tN.HTTP_ERROR=6,s=c.ErrorCode=tN,tU.COMPLETE="complete",o=c.EventType=tU,tA.EventType=tI,tI.OPEN="a",tI.CLOSE="b",tI.ERROR="c",tI.MESSAGE="d",tl.prototype.listen=tl.prototype.K,i=c.WebChannel=tA,c.FetchXmlHttpFactory=eA,eD.prototype.listenOnce=eD.prototype.L,eD.prototype.getLastError=eD.prototype.Ka,eD.prototype.getLastErrorCode=eD.prototype.Ba,eD.prototype.getStatus=eD.prototype.Z,eD.prototype.getResponseJson=eD.prototype.Oa,eD.prototype.getResponseText=eD.prototype.oa,eD.prototype.send=eD.prototype.ea,eD.prototype.setWithCredentials=eD.prototype.Ha,n=c.XhrIo=eD}).apply(void 0!==u?u:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},81115:(t,e,r)=>{"use strict";r.d(e,{AU:()=>n.AU,C3:()=>n.C3,KR:()=>n.KR,O5:()=>n.O5,TF:()=>n.TF,Zy:()=>n.Zy,hZ:()=>n.hZ});var n=r(67333)},86864:(t,e,r)=>{"use strict";let n,i,o,s;r.d(e,{P5:()=>tA});var a=r(46235),f=r(10796),h=r(49887),l=r(56391),u=r(46984);let c="@firebase/installations",p="0.6.17",d=`w:${p}`,g="FIS_v2",y=new h.FA("installations","Installations",{"missing-app-config-values":'Missing App configuration value: "{$valueName}"',"not-registered":"Firebase Installation is not registered.","installation-not-found":"Firebase Installation not found.","request-failed":'{$requestName} request failed with error "{$serverCode} {$serverStatus}: {$serverMessage}"',"app-offline":"Could not process request. Application offline.","delete-pending-registration":"Can't delete installation while there is a pending registration request."});function m(t){return t instanceof h.g&&t.code.includes("request-failed")}function v({projectId:t}){return`https://firebaseinstallations.googleapis.com/v1/projects/${t}/installations`}function b(t){return{token:t.token,requestStatus:2,expiresIn:Number(t.expiresIn.replace("s","000")),creationTime:Date.now()}}async function w(t,e){let r=(await e.json()).error;return y.create("request-failed",{requestName:t,serverCode:r.code,serverMessage:r.message,serverStatus:r.status})}function x({apiKey:t}){return new Headers({"Content-Type":"application/json",Accept:"application/json","x-goog-api-key":t})}function E(t,{refreshToken:e}){var r;let n=x(t);return n.append("Authorization",(r=e,`${g} ${r}`)),n}async function A(t){let e=await t();return e.status>=500&&e.status<600?t():e}async function I({appConfig:t,heartbeatServiceProvider:e},{fid:r}){let n=v(t),i=x(t),o=e.getImmediate({optional:!0});if(o){let t=await o.getHeartbeatsHeader();t&&i.append("x-firebase-client",t)}let s={method:"POST",headers:i,body:JSON.stringify({fid:r,authVersion:g,appId:t.appId,sdkVersion:d})},a=await A(()=>fetch(n,s));if(a.ok){let t=await a.json();return{fid:t.fid||r,registrationStatus:2,refreshToken:t.refreshToken,authToken:b(t.authToken)}}throw await w("Create Installation",a)}function S(t){return new Promise(e=>{setTimeout(e,t)})}let C=/^[cdef][\w-]{21}$/;function _(t){return`${t.appName}!${t.appId}`}let T=new Map;function O(t,e){let r=_(t);D(r,e),function(t,e){let r=function(){return!j&&"BroadcastChannel"in self&&((j=new BroadcastChannel("[Firebase] FID Change")).onmessage=t=>{D(t.data.key,t.data.fid)}),j}();r&&r.postMessage({key:t,fid:e}),function(){0===T.size&&j&&(j.close(),j=null)}()}(r,e)}function D(t,e){let r=T.get(t);if(r)for(let t of r)t(e)}let j=null,k="firebase-installations-store",B=null;function M(){return B||(B=(0,u.P2)("firebase-installations-database",1,{upgrade:(t,e)=>{0===e&&t.createObjectStore(k)}})),B}async function P(t,e){let r=_(t),n=(await M()).transaction(k,"readwrite"),i=n.objectStore(k),o=await i.get(r);return await i.put(e,r),await n.done,o&&o.fid===e.fid||O(t,e.fid),e}async function L(t){let e=_(t),r=(await M()).transaction(k,"readwrite");await r.objectStore(k).delete(e),await r.done}async function R(t,e){let r=_(t),n=(await M()).transaction(k,"readwrite"),i=n.objectStore(k),o=await i.get(r),s=e(o);return void 0===s?await i.delete(r):await i.put(s,r),await n.done,s&&(!o||o.fid!==s.fid)&&O(t,s.fid),s}async function N(t){let e;let r=await R(t.appConfig,r=>{let n=function(t,e){if(0===e.registrationStatus){if(!navigator.onLine)return{installationEntry:e,registrationPromise:Promise.reject(y.create("app-offline"))};let r={fid:e.fid,registrationStatus:1,registrationTime:Date.now()},n=U(t,r);return{installationEntry:r,registrationPromise:n}}return 1===e.registrationStatus?{installationEntry:e,registrationPromise:F(t)}:{installationEntry:e}}(t,$(r||{fid:function(){try{var t;let e=new Uint8Array(17);(self.crypto||self.msCrypto).getRandomValues(e),e[0]=112+e[0]%16;let r=(t=e,btoa(String.fromCharCode(...t)).replace(/\+/g,"-").replace(/\//g,"_").substr(0,22));return C.test(r)?r:""}catch(t){return""}}(),registrationStatus:0}));return e=n.registrationPromise,n.installationEntry});return""===r.fid?{installationEntry:await e}:{installationEntry:r,registrationPromise:e}}async function U(t,e){try{let r=await I(t,e);return P(t.appConfig,r)}catch(r){throw m(r)&&409===r.customData.serverCode?await L(t.appConfig):await P(t.appConfig,{fid:e.fid,registrationStatus:0}),r}}async function F(t){let e=await H(t.appConfig);for(;1===e.registrationStatus;)await S(100),e=await H(t.appConfig);if(0===e.registrationStatus){let{installationEntry:e,registrationPromise:r}=await N(t);return r||e}return e}function H(t){return R(t,t=>{if(!t)throw y.create("installation-not-found");return $(t)})}function $(t){var e;return 1===(e=t).registrationStatus&&e.registrationTime+1e4<Date.now()?{fid:t.fid,registrationStatus:0}:t}async function z({appConfig:t,heartbeatServiceProvider:e},r){let n=function(t,{fid:e}){return`${v(t)}/${e}/authTokens:generate`}(t,r),i=E(t,r),o=e.getImmediate({optional:!0});if(o){let t=await o.getHeartbeatsHeader();t&&i.append("x-firebase-client",t)}let s={method:"POST",headers:i,body:JSON.stringify({installation:{sdkVersion:d,appId:t.appId}})},a=await A(()=>fetch(n,s));if(a.ok)return b(await a.json());throw await w("Generate Auth Token",a)}async function V(t,e=!1){let r;let n=await R(t.appConfig,n=>{var i;if(!J(n))throw y.create("not-registered");let o=n.authToken;if(!e&&2===(i=o).requestStatus&&!function(t){let e=Date.now();return e<t.creationTime||t.creationTime+t.expiresIn<e+36e5}(i))return n;if(1===o.requestStatus)return r=W(t,e),n;{if(!navigator.onLine)throw y.create("app-offline");let e=function(t){let e={requestStatus:1,requestTime:Date.now()};return Object.assign(Object.assign({},t),{authToken:e})}(n);return r=K(t,e),e}});return r?await r:n.authToken}async function W(t,e){let r=await X(t.appConfig);for(;1===r.authToken.requestStatus;)await S(100),r=await X(t.appConfig);let n=r.authToken;return 0===n.requestStatus?V(t,e):n}function X(t){return R(t,t=>{var e;if(!J(t))throw y.create("not-registered");return 1===(e=t.authToken).requestStatus&&e.requestTime+1e4<Date.now()?Object.assign(Object.assign({},t),{authToken:{requestStatus:0}}):t})}async function K(t,e){try{let r=await z(t,e),n=Object.assign(Object.assign({},e),{authToken:r});return await P(t.appConfig,n),r}catch(r){if(m(r)&&(401===r.customData.serverCode||404===r.customData.serverCode))await L(t.appConfig);else{let r=Object.assign(Object.assign({},e),{authToken:{requestStatus:0}});await P(t.appConfig,r)}throw r}}function J(t){return void 0!==t&&2===t.registrationStatus}async function q(t){let{installationEntry:e,registrationPromise:r}=await N(t);return r?r.catch(console.error):V(t).catch(console.error),e.fid}async function G(t,e=!1){return await Y(t),(await V(t,e)).token}async function Y(t){let{registrationPromise:e}=await N(t);e&&await e}function Z(t){return y.create("missing-app-config-values",{valueName:t})}let Q="installations";(0,a.om)(new l.uA(Q,t=>{let e=t.getProvider("app").getImmediate(),r=function(t){if(!t||!t.options)throw Z("App Configuration");if(!t.name)throw Z("App Name");for(let e of["projectId","apiKey","appId"])if(!t.options[e])throw Z(e);return{appName:t.name,projectId:t.options.projectId,apiKey:t.options.apiKey,appId:t.options.appId}}(e),n=(0,a.j6)(e,"heartbeat");return{app:e,appConfig:r,heartbeatServiceProvider:n,_delete:()=>Promise.resolve()}},"PUBLIC")),(0,a.om)(new l.uA("installations-internal",t=>{let e=t.getProvider("app").getImmediate(),r=(0,a.j6)(e,Q).getImmediate();return{getId:()=>q(r),getToken:t=>G(r,t)}},"PRIVATE")),(0,a.KO)(c,p),(0,a.KO)(c,p,"esm2017");let tt="analytics",te="https://www.googletagmanager.com/gtag/js",tr=new f.Vy("@firebase/analytics"),tn=new h.FA("analytics","Analytics",{"already-exists":"A Firebase Analytics instance with the appId {$id}  already exists. Only one Firebase Analytics instance can be created for each appId.","already-initialized":"initializeAnalytics() cannot be called again with different options than those it was initially called with. It can be called again with the same options to return the existing instance, or getAnalytics() can be used to get a reference to the already-initialized instance.","already-initialized-settings":"Firebase Analytics has already been initialized.settings() must be called before initializing any Analytics instanceor it will have no effect.","interop-component-reg-failed":"Firebase Analytics Interop Component failed to instantiate: {$reason}","invalid-analytics-context":"Firebase Analytics is not supported in this environment. Wrap initialization of analytics in analytics.isSupported() to prevent initialization in unsupported environments. Details: {$errorInfo}","indexeddb-unavailable":"IndexedDB unavailable or restricted in this environment. Wrap initialization of analytics in analytics.isSupported() to prevent initialization in unsupported environments. Details: {$errorInfo}","fetch-throttle":"The config fetch request timed out while in an exponential backoff state. Unix timestamp in milliseconds when fetch request throttling ends: {$throttleEndTimeMillis}.","config-fetch-failed":"Dynamic config fetch failed: [{$httpStatus}] {$responseMessage}","no-api-key":'The "apiKey" field is empty in the local Firebase config. Firebase Analytics requires this field tocontain a valid API key.',"no-app-id":'The "appId" field is empty in the local Firebase config. Firebase Analytics requires this field tocontain a valid app ID.',"no-client-id":'The "client_id" field is empty.',"invalid-gtag-resource":"Trusted Types detected an invalid gtag resource: {$gtagURL}."});function ti(t){if(!t.startsWith(te)){let e=tn.create("invalid-gtag-resource",{gtagURL:t});return tr.warn(e.message),""}return t}function to(t){return Promise.all(t.map(t=>t.catch(t=>t)))}async function ts(t,e,r,n,i,o){let s=n[i];try{if(s)await e[s];else{let t=(await to(r)).find(t=>t.measurementId===i);t&&await e[t.appId]}}catch(t){tr.error(t)}t("config",i,o)}async function ta(t,e,r,n,i){try{let o=[];if(i&&i.send_to){let t=i.send_to;Array.isArray(t)||(t=[t]);let n=await to(r);for(let r of t){let t=n.find(t=>t.measurementId===r),i=t&&e[t.appId];if(i)o.push(i);else{o=[];break}}}0===o.length&&(o=Object.values(e)),await Promise.all(o),t("event",n,i||{})}catch(t){tr.error(t)}}class tf{constructor(t={},e=1e3){this.throttleMetadata=t,this.intervalMillis=e}getThrottleMetadata(t){return this.throttleMetadata[t]}setThrottleMetadata(t,e){this.throttleMetadata[t]=e}deleteThrottleMetadata(t){delete this.throttleMetadata[t]}}let th=new tf;async function tl(t){var e;let{appId:r,apiKey:n}=t,i={method:"GET",headers:new Headers({Accept:"application/json","x-goog-api-key":n})},o="https://firebase.googleapis.com/v1alpha/projects/-/apps/{app-id}/webConfig".replace("{app-id}",r),s=await fetch(o,i);if(200!==s.status&&304!==s.status){let t="";try{let r=await s.json();(null===(e=r.error)||void 0===e?void 0:e.message)&&(t=r.error.message)}catch(t){}throw tn.create("config-fetch-failed",{httpStatus:s.status,responseMessage:t})}return s.json()}async function tu(t,e=th,r){let{appId:n,apiKey:i,measurementId:o}=t.options;if(!n)throw tn.create("no-app-id");if(!i){if(o)return{measurementId:o,appId:n};throw tn.create("no-api-key")}let s=e.getThrottleMetadata(n)||{backoffCount:0,throttleEndTimeMillis:Date.now()},a=new tp;return setTimeout(async()=>{a.abort()},void 0!==r?r:6e4),tc({appId:n,apiKey:i,measurementId:o},s,a,e)}async function tc(t,{throttleEndTimeMillis:e,backoffCount:r},n,i=th){var o,s,a;let{appId:f,measurementId:l}=t;try{await (s=n,a=e,new Promise((t,e)=>{let r=setTimeout(t,Math.max(a-Date.now(),0));s.addEventListener(()=>{clearTimeout(r),e(tn.create("fetch-throttle",{throttleEndTimeMillis:a}))})}))}catch(t){if(l)return tr.warn(`Timed out fetching this Firebase app's measurement ID from the server. Falling back to the measurement ID ${l} provided in the "measurementId" field in the local Firebase config. [${null==t?void 0:t.message}]`),{appId:f,measurementId:l};throw t}try{let e=await tl(t);return i.deleteThrottleMetadata(f),e}catch(a){if(!function(t){if(!(t instanceof h.g)||!t.customData)return!1;let e=Number(t.customData.httpStatus);return 429===e||500===e||503===e||504===e}(a)){if(i.deleteThrottleMetadata(f),l)return tr.warn(`Failed to fetch this Firebase app's measurement ID from the server. Falling back to the measurement ID ${l} provided in the "measurementId" field in the local Firebase config. [${null==a?void 0:a.message}]`),{appId:f,measurementId:l};throw a}let e=503===Number(null===(o=null==a?void 0:a.customData)||void 0===o?void 0:o.httpStatus)?(0,h.p9)(r,i.intervalMillis,30):(0,h.p9)(r,i.intervalMillis),s={throttleEndTimeMillis:Date.now()+e,backoffCount:r+1};return i.setThrottleMetadata(f,s),tr.debug(`Calling attemptFetch again in ${e} millis`),tc(t,s,n,i)}}class tp{constructor(){this.listeners=[]}addEventListener(t){this.listeners.push(t)}abort(){this.listeners.forEach(t=>t())}}async function td(t,e,r,n,i){if(i&&i.global){t("event",r,n);return}{let i=await e;t("event",r,Object.assign(Object.assign({},n),{send_to:i}))}}async function tg(){if(!(0,h.zW)())return tr.warn(tn.create("indexeddb-unavailable",{errorInfo:"IndexedDB is not available in this environment."}).message),!1;try{await (0,h.eX)()}catch(t){return tr.warn(tn.create("indexeddb-unavailable",{errorInfo:null==t?void 0:t.toString()}).message),!1}return!0}async function ty(t,e,r,o,s,a,f){var h;let l=tu(t);l.then(e=>{r[e.measurementId]=e.appId,t.options.measurementId&&e.measurementId!==t.options.measurementId&&tr.warn(`The measurement ID in the local Firebase config (${t.options.measurementId}) does not match the measurement ID fetched from the server (${e.measurementId}). To ensure analytics events are always sent to the correct Analytics property, update the measurement ID field in the local config or remove it from the local config.`)}).catch(t=>tr.error(t)),e.push(l);let u=tg().then(t=>t?o.getId():void 0),[c,p]=await Promise.all([l,u]);!function(t){for(let e of Object.values(window.document.getElementsByTagName("script")))if(e.src&&e.src.includes(te)&&e.src.includes(t))return e;return null}(a)&&function(t,e){let r;let n=(window.trustedTypes&&(r=window.trustedTypes.createPolicy("firebase-js-sdk-policy",{createScriptURL:ti})),r),i=document.createElement("script"),o=`${te}?l=${t}&id=${e}`;i.src=n?null==n?void 0:n.createScriptURL(o):o,i.async=!0,document.head.appendChild(i)}(a,c.measurementId),i&&(s("consent","default",i),i=void 0),s("js",new Date);let d=null!==(h=null==f?void 0:f.config)&&void 0!==h?h:{};return d.origin="firebase",d.update=!0,null!=p&&(d.firebase_id=p),s("config",c.measurementId,d),n&&(s("set",n),n=void 0),c.measurementId}class tm{constructor(t){this.app=t}_delete(){return delete tv[this.app.options.appId],Promise.resolve()}}let tv={},tb=[],tw={},tx="dataLayer",tE=!1;function tA(t=(0,a.Sx)()){t=(0,h.Ku)(t);let e=(0,a.j6)(t,tt);return e.isInitialized()?e.getImmediate():function(t,e={}){let r=(0,a.j6)(t,tt);if(r.isInitialized()){let t=r.getImmediate();if((0,h.bD)(e,r.getOptions()))return t;throw tn.create("already-initialized")}return r.initialize({options:e})}(t)}let tI="@firebase/analytics",tS="0.10.16";(0,a.om)(new l.uA(tt,(t,{options:e})=>(function(t,e,r){!function(){let t=[];if((0,h.sr)()&&t.push("This is a browser extension environment."),(0,h.dM)()||t.push("Cookies are not available."),t.length>0){let e=t.map((t,e)=>`(${e+1}) ${t}`).join(" "),r=tn.create("invalid-analytics-context",{errorInfo:e});tr.warn(r.message)}}();let n=t.options.appId;if(!n)throw tn.create("no-app-id");if(!t.options.apiKey){if(t.options.measurementId)tr.warn(`The "apiKey" field is empty in the local Firebase config. This is needed to fetch the latest measurement ID for this Firebase app. Falling back to the measurement ID ${t.options.measurementId} provided in the "measurementId" field in the local Firebase config.`);else throw tn.create("no-api-key")}if(null!=tv[n])throw tn.create("already-exists",{id:n});if(!tE){var i,a;let t,e;t=[],Array.isArray(window[tx])?t=window[tx]:window[tx]=t;let{wrappedGtag:r,gtagCore:n}=(i="gtag",e=function(...t){window[tx].push(arguments)},window[i]&&"function"==typeof window[i]&&(e=window[i]),window[i]=(a=e,async function(t,...e){try{if("event"===t){let[t,r]=e;await ta(a,tv,tb,t,r)}else if("config"===t){let[t,r]=e;await ts(a,tv,tb,tw,t,r)}else if("consent"===t){let[t,r]=e;a("consent",t,r)}else if("get"===t){let[t,r,n]=e;a("get",t,r,n)}else if("set"===t){let[t]=e;a("set",t)}else a(t,...e)}catch(t){tr.error(t)}}),{gtagCore:e,wrappedGtag:window[i]});s=r,o=n,tE=!0}return tv[n]=ty(t,tb,tw,e,o,tx,r),new tm(t)})(t.getProvider("app").getImmediate(),t.getProvider("installations-internal").getImmediate(),e),"PUBLIC")),(0,a.om)(new l.uA("analytics-internal",function(t){try{let e=t.getProvider(tt).getImmediate();return{logEvent:(t,r,n)=>{var i;return i=e,void(i=(0,h.Ku)(i),td(s,tv[i.app.options.appId],t,r,n).catch(t=>tr.error(t)))}}}catch(t){throw tn.create("interop-component-reg-failed",{reason:t})}},"PRIVATE")),(0,a.KO)(tI,tS),(0,a.KO)(tI,tS,"esm2017")}}]);